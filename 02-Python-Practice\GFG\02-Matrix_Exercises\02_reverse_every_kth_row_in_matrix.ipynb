{"cells": [{"cell_type": "markdown", "id": "5c7f11c1", "metadata": {}, "source": ["# Task: Reverse Every Kth Row in a Matrix\n", "\n", "## Problem Statement:\n", "We are given a matrix (a list of lists) and an integer `K`. Our task is to reverse every Kth row in the matrix.\n", "\n", "### Steps:\n", "1. Loop through the matrix using the row index.\n", "2. Check if the current row is the Kth row (i.e., (index + 1) is divisible by `K`).\n", "3. If it is, reverse the row in place.\n", "4. Return the matrix after all Kth rows have been reversed.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e75c4e7b", "metadata": {}, "outputs": [], "source": ["def reverse_kth_row(a,k):\n", "    res = []\n", "    for i,ele in enumerate(a):\n", "        if (i + 1) % K == 0:\n", "            res.append(list(reversed(ele)))\n", "        else:\n", "            res.append(ele)\n", "    \n", "    return res"]}, {"cell_type": "code", "execution_count": 2, "id": "d65ec8dc", "metadata": {}, "outputs": [], "source": ["a = [[5, 3, 2], [8, 6, 3], [3, 5, 2], [3, 6], [3, 7, 4], [2, 9]]\n", "K = 3"]}, {"cell_type": "code", "execution_count": 3, "id": "02af99a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[5, 3, 2], [8, 6, 3], [2, 5, 3], [3, 6], [3, 7, 4], [9, 2]]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["reverse_kth_row(a,K)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}