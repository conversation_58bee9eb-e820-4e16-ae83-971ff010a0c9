{"cells": [{"cell_type": "markdown", "id": "c3a2bd0e-3830-4f12-ac50-d548de87aa77", "metadata": {}, "source": ["# Task: Create a List of Objects in Python Class\n", "\n", "## Problem Statement:\n", "In Python, creating a list of objects involves storing instances of a class in a list. This allows for efficient access, modification, and iteration over multiple objects. For example, with a `Geeks` class having attributes like `name` and `roll`, a list can be used to manage multiple student records.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "945289b8-5850-49cb-9db5-e197eecd24a9", "metadata": {}, "outputs": [], "source": ["class Geeks:\n", "    def __init__(self, name, roll):\n", "        self.name = name\n", "        self.roll = roll"]}, {"cell_type": "code", "execution_count": 3, "id": "32dfc2db-1c61-479c-8ff4-d3b7b86a5c87", "metadata": {}, "outputs": [], "source": ["lst = [('<PERSON><PERSON><PERSON>', 2), ('<PERSON><PERSON>', 40), ('<PERSON>', 44), ('<PERSON><PERSON>', 67)]"]}, {"cell_type": "markdown", "id": "c05f0d7a-565f-4344-b66e-2559680681d6", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### Using list comprehension\n", "\n", "1. Define a class with the required attributes.\n", "2. Prepare the input data (e.g., list of names and roll numbers).\n", "3. Use list comprehension to create a list of class instances from the input data.\n", "4. Store and access the objects from the list as needed.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6bb9d5cf-8a24-4a1c-a17f-c4cc207273e4", "metadata": {}, "outputs": [], "source": ["a = [Geeks(name, roll) for name, roll in lst]"]}, {"cell_type": "code", "execution_count": 5, "id": "9152a7e7-e4e5-439d-8fdf-2fab3e36722e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Akash 2\n", "<PERSON><PERSON> 40\n", "Reaper 44\n", "<PERSON>eer 67\n"]}], "source": ["for obj in a:\n", "    print(obj.name, obj.roll, sep=' ')"]}, {"cell_type": "markdown", "id": "efd55136-129c-4d9e-a136-ea49ff6cb78b", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### Using map()\n", "\n", "1. Define a class with the required attributes.\n", "2. Prepare an iterable containing the input data for object creation.\n", "3. Use the `map()` function to apply the class constructor to each item in the iterable.\n", "4. Convert the resulting map object to a list to store the created objects.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "70835a2d-9795-4e33-a25f-d93e0b1ebf30", "metadata": {}, "outputs": [], "source": ["a = list(map(lambda x: Geeks(x[0], x[1]), lst))"]}, {"cell_type": "code", "execution_count": 7, "id": "b8ee5f1c-ede0-41d1-8ec2-9c1f4ac58105", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Akash 2\n", "<PERSON><PERSON> 40\n", "Reaper 44\n", "<PERSON>eer 67\n"]}], "source": ["for obj in a:\n", "    print(obj.name, obj.roll, sep=' ')"]}, {"cell_type": "markdown", "id": "90dce026-b60d-4bb0-a368-27489b40f553", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### Using for loop\n", "\n", "1. Define a class with the required attributes.\n", "2. Prepare the input data (e.g., list of tuples or dictionaries).\n", "3. Initialize an empty list to store the objects.\n", "4. Use a `for` loop to iterate over the input data.\n", "5. In each iteration, create an object and append it to the list.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "507dfccd-0301-442b-8ad9-6b07ab8edcae", "metadata": {}, "outputs": [], "source": ["a = []\n", "for data in lst:\n", "    a.append(Geeks(data[0], data[1]))"]}, {"cell_type": "code", "execution_count": 9, "id": "57d9468f-54c1-4827-be1e-bc8b5a5c608b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Akash 2\n", "<PERSON><PERSON> 40\n", "Reaper 44\n", "<PERSON>eer 67\n"]}], "source": ["for obj in a:\n", "    print(obj.name, obj.roll, sep=' ')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}