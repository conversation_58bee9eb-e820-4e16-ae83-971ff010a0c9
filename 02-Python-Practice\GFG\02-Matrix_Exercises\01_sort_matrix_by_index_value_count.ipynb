{"cells": [{"cell_type": "markdown", "id": "464c4c29", "metadata": {}, "source": ["# Task: Sort Matrix by Index-Value Equality Count\n", "\n", "## Problem Statement:\n", "You are given a matrix (list of lists). Your task is to sort the rows of the matrix based on the number of elements in each row that match their respective index values.\n", "\n", "### Steps:\n", "1. For each row in the matrix, count how many elements in the row are equal to their respective index (i.e., the value at index `i` should be equal to `i`).\n", "2. Sort the rows based on the number of these index-value matches.\n", "3. Output the matrix after sorting the rows in increasing order of the number of index-value matches."]}, {"cell_type": "code", "execution_count": 1, "id": "19daa47d", "metadata": {}, "outputs": [], "source": ["def count_index_value_equality(row):\n", "    return sum(1 for i, val in enumerate(row) if i == val)"]}, {"cell_type": "code", "execution_count": 2, "id": "d512ebda", "metadata": {}, "outputs": [], "source": ["def sort_matrix_by_index_value_count(matrix):\n", "    row_counts = [count_index_value_equality(row) for row in matrix]\n", "    \n", "    sorted_matrix = [row for _, row in sorted(zip(row_counts, matrix), key=lambda x: x[0])]\n", "    \n", "    return sorted_matrix"]}, {"cell_type": "code", "execution_count": 3, "id": "7feb75f5", "metadata": {}, "outputs": [], "source": ["test_matrix = [\n", "    [3, 1, 2, 5, 4],\n", "    [0, 1, 2, 3, 4],\n", "    [6, 5, 4, 3, 2],\n", "    [0, 5, 4, 2]\n", "]\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e411fe78", "metadata": {}, "outputs": [], "source": ["sorted_matrix = sort_matrix_by_index_value_count(test_matrix)"]}, {"cell_type": "code", "execution_count": 5, "id": "6aebcfb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[6, 5, 4, 3, 2], [0, 5, 4, 2], [3, 1, 2, 5, 4], [0, 1, 2, 3, 4]]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted_matrix"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}