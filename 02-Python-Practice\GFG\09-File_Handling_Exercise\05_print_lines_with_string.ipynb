{"cells": [{"cell_type": "markdown", "id": "d1057c34", "metadata": {}, "source": ["# Task: Print Lines Containing a Given String in a File\n", "\n", "## Problem Statement:\n", "Write a Python program that takes a string input and prints all the lines from a text file (`geeks.txt`) that contain the given string.\n", "\n", "## Steps:\n", "1. **Open the file** in read mode using `with open('geeks.txt', 'r')`.\n", "2. **Take the target string** as input from the user.\n", "3. **Loop through each line** of the file.\n", "4. **Check if the target string** is present in the current line using `if target_string in line`.\n", "5. **Print the line** if it contains the target string.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5fbfb12e", "metadata": {}, "outputs": [], "source": ["file_name = input(\"Enter the File's Name: \")"]}, {"cell_type": "code", "execution_count": 2, "id": "84ae4935", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "**** Lines containing \"better\" ****\n", "\n", "Explicit is better than implicit.\n", "Simple is better than complex.\n", "Complex is better than complicated.\n", "Flat is better than nested.\n", "Sparse is better than dense.\n", "\n"]}], "source": ["try:\n", "    file_read = open(file_name,'r')\n", "    text = input(\"Enter the string\")\n", "    lines = file_read.readlines()\n", "    new_list = []\n", "    idx = 0\n", "\n", "    for line in lines:\n", "        if text in line:\n", "            new_list.insert(idx, line)\n", "            idx += 1\n", "        \n", "    file_read.close()\n", "\n", "    if len(new_list) == 0:\n", "        print(\"\\n\\\"\" + text + \"\\\" is not found in \\\"\" + file_name + \"\\\"!\")\n", "    else:\n", "        linelen = len(new_list)\n", "        print(\"\\n**** Lines containing \\\"\" + text + \"\\\" ****\\n\")\n", "        for i in range(linelen):\n", "            print(end=new_list[i])\n", "        print()\n", "except:\n", "    print(\"\\nThe file doesn't exist!\")"]}, {"cell_type": "code", "execution_count": null, "id": "4e925ec6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}