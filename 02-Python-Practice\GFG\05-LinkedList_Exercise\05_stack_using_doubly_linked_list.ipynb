{"cells": [{"cell_type": "markdown", "id": "659626b5", "metadata": {}, "source": ["# Task: Python | Stack using Doubly Linked List\n", "\n", "## Problem Statement:\n", "Implement a **Stack** using a **Doubly Linked List** in Python. A stack is a linear data structure following the **Last-In-First-Out (LIFO)** principle. The key advantage of using a linked list over an array for stack implementation is dynamic memory allocation, avoiding stack overflow due to fixed size limits.\n", "\n", "## Steps:\n", "\n", "1. **Define a Node class** with `data`, `prev`, and `next` pointers.\n", "2. **Create a DoublyLinkedListStack class** to manage stack operations.\n", "3. Implement the following stack methods:\n", "   - `push(data)`: Add element to the top of the stack.\n", "   - `pop()`: Remove and return the top element from the stack.\n", "   - `top()`: Return the value of the top element without removing it.\n", "   - `size()`: Return the current number of elements in the stack.\n", "   - `isEmpty()`: Return `True` if the stack is empty, else `False`.\n", "   - `printstack()`: Print all elements from top to bottom.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5f1aead8", "metadata": {}, "outputs": [], "source": ["class Node:\n", "    def __init__(self, data):\n", "        self.data = data\n", "        self.next = None\n", "        self.prev = None"]}, {"cell_type": "code", "execution_count": 2, "id": "989bd098", "metadata": {}, "outputs": [], "source": ["class Stack:\n", "    def __init__(self):\n", "        self.head = None\n", "    \n", "    def push(self,data):\n", "        if self.head is None:\n", "            self.head = Node(data)\n", "        else:\n", "            new_node = Node(data)\n", "            self.head.prev = new_node\n", "            new_node.next = self.head\n", "            new_node.prev = None\n", "            self.head = new_node\n", "        \n", "    def pop(self):\n", "        if self.head is None:\n", "            return None\n", "        elif self.head.next is None:\n", "            temp = self.head.data\n", "            self.head = None\n", "            return temp\n", "        else:\n", "            temp = self.head.data\n", "            self.head = self.head.next\n", "            self.head.prev = None\n", "            return temp\n", "        \n", "    def top(self):\n", "        return self.head.data\n", "\n", "    def size(self):\n", "        temp = self.head\n", "        count = 0\n", "        while temp is not None:\n", "            count += 1\n", "            temp = temp.next\n", "        return count\n", "\n", "    def is<PERSON><PERSON>y(self):\n", "        return self.head is None\n", "    \n", "    def printstack(self):\n", "        print(\"stack elements are:\")\n", "        temp = self.head\n", "        while temp is not None:\n", "            print(temp.data,end=\"=>\")\n", "            temp = temp.next\n", "        print()"]}, {"cell_type": "code", "execution_count": 3, "id": "9daec2dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stack operations using Doubly LinkedList\n", "stack elements are:\n", "7=>6=>5=>4=>\n", "\n", "Top element is 7\n", "Size of the stack is: 4\n", "stack elements are:\n", "5=>4=>\n", "Stack is empty: False\n"]}], "source": ["if __name__ == '__main__':\n", "    stack = Stack()\n", "    print(\"Stack operations using Doubly LinkedList\")\n", "    stack.push(4)\n", "    stack.push(5)\n", "    stack.push(6)\n", "    stack.push(7)\n", "    stack.printstack()\n", "    print(\"\\nTop element is\", stack.top())\n", "    print(\"Size of the stack is:\",stack.size())\n", "\n", "    stack.pop()\n", "    stack.pop()\n", "\n", "    stack.printstack()\n", "\n", "    print(\"Stack is empty:\",stack.isEmpty())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}