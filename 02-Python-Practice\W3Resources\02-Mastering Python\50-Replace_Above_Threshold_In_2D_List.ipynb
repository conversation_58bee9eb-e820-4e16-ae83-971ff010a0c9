{"cells": [{"cell_type": "markdown", "id": "45869dac", "metadata": {}, "source": ["# Task: Replace Values Above Threshold in 2D List\n", "\n", "## Problem Statement:\n", "Write a Python program that creates a **2D list (matrix)** filled with random integers, then **replaces all values greater than a given threshold** with that threshold value.\n", "\n", "## Steps:\n", "1. **Generate a 2D list** with random integers using nested loops.\n", "2. Define a **threshold value**.\n", "3. **Iterate through each element** in the 2D list.\n", "4. If an element is **greater than the threshold**, replace it with the threshold.\n", "5. **Print or return** the modified 2D list.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6c525903", "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 2, "id": "c065b7a5", "metadata": {}, "outputs": [], "source": ["matrix = [[random.randint(0,100) for _ in range(4)] for _ in range(3)]"]}, {"cell_type": "code", "execution_count": 3, "id": "2a9d6e5d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[31, 30, 47, 86], [70, 49, 38, 42], [53, 62, 84, 44]]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["matrix"]}, {"cell_type": "code", "execution_count": 4, "id": "87050e4e", "metadata": {}, "outputs": [], "source": ["threashold = 75"]}, {"cell_type": "code", "execution_count": 5, "id": "bc776d7b", "metadata": {}, "outputs": [], "source": ["matrix = [[min(x, threashold) for x in row] for row in matrix]"]}, {"cell_type": "code", "execution_count": 6, "id": "2dbd950f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[31, 30, 47, 75], [70, 49, 38, 42], [53, 62, 75, 44]]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["matrix"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}