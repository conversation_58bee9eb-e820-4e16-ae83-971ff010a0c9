{"cells": [{"cell_type": "markdown", "id": "d07092a1", "metadata": {}, "source": ["# Module 2"]}, {"cell_type": "markdown", "id": "43292cb4-8f6d-45e1-aad1-bdbae03f1344", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Numbers"]}, {"cell_type": "code", "execution_count": 1, "id": "7b10146f-6871-441c-95c5-1df3060d8346", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["1+1"]}, {"cell_type": "code", "execution_count": 2, "id": "06d3e189-589a-4fb1-9f13-a707b16b6ff1", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["1-1"]}, {"cell_type": "code", "execution_count": 3, "id": "d5320346-d7d2-4f3b-a0e2-e85bcf604fc2", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["1*1"]}, {"cell_type": "code", "execution_count": 4, "id": "dd34b3f5-1379-411d-b3af-f19bdf6774ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["1/1"]}, {"cell_type": "code", "execution_count": 5, "id": "6d9f41de-58c2-4b96-ab5d-f311d8b895a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["50 % 2 # Modulo Operation"]}, {"cell_type": "code", "execution_count": 6, "id": "ed78e50d-b88c-445b-a65b-e1c22faf61f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["50 % 3"]}, {"cell_type": "code", "execution_count": 7, "id": "f9667497-cc04-454b-a465-7e42a284b8a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["105"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["2 + 10 * 10 + 3"]}, {"cell_type": "code", "execution_count": 8, "id": "876b2d5e-5f28-4d73-b595-3c02a0fd4f4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["156"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["(2 + 10) * (10 + 3)"]}, {"cell_type": "code", "execution_count": 9, "id": "182122b9-cc57-434a-ab29-8662bf81efee", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["2 ** 3 #Power"]}, {"cell_type": "markdown", "id": "277f3d18-8d3f-4144-9ae6-b2c01bfb74b7", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Variables"]}, {"cell_type": "markdown", "id": "b957f5bd-07d2-4047-bddf-8cbe43d40b4d", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["**Static Typing vs Dynamic Typing**\n", "\n", "**Static Typing** is used in languages like C, C++, and Java. In these languages, the type of a variable (such as `int`, `float`, or `string`) must be explicitly declared by the programmer. The type is checked at compile time, which helps catch type-related errors before the program runs.\n", "\n", "**Dynamic Typing** is used in Python. In Python, you do not need to specify the type of a variable. The interpreter automatically determines the variable's type based on the value assigned to it. The type is checked at runtime, which makes the code more flexible but may lead to runtime errors if types are misused.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "1e4b28e4-b518-49a5-9973-b6ff84388474", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a = 5\n", "a"]}, {"cell_type": "code", "execution_count": 11, "id": "fc71ccc5-b36d-4bec-9dbe-1d3aa123bb24", "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["a = 10\n", "a"]}, {"cell_type": "code", "execution_count": 12, "id": "b01213a1-4892-42f3-8e18-095a7d7ed6ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["a+a"]}, {"cell_type": "code", "execution_count": 13, "id": "f445c46a-029b-4771-b652-81c713e83367", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a = a+a\n", "a"]}, {"cell_type": "code", "execution_count": 14, "id": "03c6a148-b415-4e60-be27-9f908404053d", "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["type(a)"]}, {"cell_type": "code", "execution_count": 15, "id": "8ec1160c-96b3-405c-bb1c-a397dfb821a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["a = 30.1\n", "type(a)"]}, {"cell_type": "code", "execution_count": 16, "id": "a32d361b-116d-4777-a021-88ae6122a706", "metadata": {}, "outputs": [{"data": {"text/plain": ["172800.0"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["principal = 120000\n", "roi = 12\n", "years = 12\n", "\n", "interest = principal * roi * years / 100\n", "interest"]}, {"cell_type": "markdown", "id": "7b441315-52ee-430a-adcd-faeadca7339b", "metadata": {}, "source": ["## Strings"]}, {"cell_type": "code", "execution_count": 17, "id": "ae4c1bc5-58c7-4d78-bd4d-78681f22281c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["\"Hello\""]}, {"cell_type": "code", "execution_count": 18, "id": "536c63b4-0ea7-4375-b5c5-2c242da8cd90", "metadata": {}, "outputs": [{"data": {"text/plain": ["'World'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["'World'"]}, {"cell_type": "code", "execution_count": 19, "id": "b701087b-4c85-4281-a5b1-577c08d43bf2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"I'm going to run\""]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 'I'm going to run' //this will give error use \"\" instead.\n", "\"I'm going to run\""]}, {"cell_type": "code", "execution_count": 20, "id": "4d48386d-2c3d-4ddd-a1d1-4be3b94977b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello \n", "I am MR!\n"]}], "source": ["print(\"Hello \\nI am MR!\")"]}, {"cell_type": "code", "execution_count": 21, "id": "4b67de1b-2213-48de-a02b-c8186715487e", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(\"Hello\")"]}, {"cell_type": "code", "execution_count": 22, "id": "0b27b7f5-bd7d-4a87-bc66-33cf9a189d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(\"Hello World!\")"]}, {"cell_type": "markdown", "id": "01e472b0-05dd-4ef7-b7d2-b63f4d4b136d", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Indexing and Slicing"]}, {"cell_type": "code", "execution_count": 23, "id": "b3c021fb-7bbf-47d4-ac69-e90d9e646b7d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello World'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr = \"Hello World\"\n", "mystr"]}, {"cell_type": "code", "execution_count": 24, "id": "23fbc008-dd6e-4cd1-a748-a184aeaef7a9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'H'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[0]"]}, {"cell_type": "code", "execution_count": 25, "id": "87438c98-d299-4e03-8c28-c<PERSON><PERSON><PERSON><PERSON>38d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'d'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[-1]"]}, {"cell_type": "code", "execution_count": 26, "id": "25fc33f5-a477-4496-b36e-178f1e3ff2f9", "metadata": {}, "outputs": [], "source": ["mystr = \"ABCDEFGHIJK\""]}, {"cell_type": "code", "execution_count": 27, "id": "40d0ba8b-3d9b-41c9-95bb-3563c17870a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'CDEFGHIJK'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[2:]"]}, {"cell_type": "code", "execution_count": 28, "id": "dd707429-3e39-412d-b23c-20049bb17e86", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ABC'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[:3]"]}, {"cell_type": "code", "execution_count": 29, "id": "cd293347-ea1f-41e9-a94b-40bd35aa4981", "metadata": {}, "outputs": [{"data": {"text/plain": ["'BCD'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[1:4]"]}, {"cell_type": "code", "execution_count": 30, "id": "b567a973-3049-42f0-aff5-70aceb2929d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ACEG'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[0:8:2]"]}, {"cell_type": "markdown", "id": "f941a96b-15c6-4282-8884-120bb56340a8", "metadata": {}, "source": ["**Python Slicing**\n", "\n", "Slicing is a concise way to extract parts of sequences like lists, strings, or tuples in Python.\n", "\n", "**Syntax**\n", "\n", "sequence[start:stop:step]\n", "start – The beginning index of the slice (inclusive).\n", "\n", "stop – The ending index of the slice (exclusive).\n", "\n", "step – The step between elements (optional).\n", "\n", "**Key Points**\n", "\n", "All three parameters are optional.\n", "\n", "Negative indices count from the end of the sequence.\n", "\n", "Slicing returns a new sequence of the same type.\n", "\n", "Original sequences are not modified.\n", "\n", "**Common Uses**\n", "\n", "Accessing subsets of data.\n", "\n", "Reversing sequences.\n", "\n", "Skipping elements."]}, {"cell_type": "code", "execution_count": 31, "id": "92830d93-e71f-4e42-9cec-123bc922b8ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["'KJIHGFEDCBA'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr[::-1]"]}, {"cell_type": "markdown", "id": "60ab3c6f", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## String properties and Methods"]}, {"cell_type": "markdown", "id": "389c58b9", "metadata": {}, "source": ["### Immutability"]}, {"cell_type": "code", "execution_count": 32, "id": "895c59c9", "metadata": {}, "outputs": [], "source": ["name = \"MR\""]}, {"cell_type": "code", "execution_count": 33, "id": "d45e2b36", "metadata": {}, "outputs": [], "source": ["#name[0] = 'S' #This is not allowed as Strings are immutable"]}, {"cell_type": "code", "execution_count": 34, "id": "92905847", "metadata": {}, "outputs": [{"data": {"text/plain": ["'R'"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["last_letters = name[1:]\n", "last_letters"]}, {"cell_type": "code", "execution_count": 35, "id": "9691c1ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["'NR'"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["'N' + last_letters # This is called String concatination"]}, {"cell_type": "code", "execution_count": 36, "id": "6408ec8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ZZZZZZZZZZ'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["letter= 'Z'\n", "\n", "letter = letter * 10\n", "\n", "letter"]}, {"cell_type": "code", "execution_count": 38, "id": "801e52f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'abcdef<PERSON><PERSON><PERSON>'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr.lower()"]}, {"cell_type": "code", "execution_count": 39, "id": "abaf479b-a31d-4edd-a389-eb26bf986821", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ABCDEFGHIJK'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["mystr.upper()"]}, {"cell_type": "code", "execution_count": 41, "id": "83787e23-17e9-4374-ba42-1950e5fa4393", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Hello', 'World']"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["x = 'Hello World'\n", "x.split()"]}, {"cell_type": "code", "execution_count": 43, "id": "3ac9af04-d0e7-4354-b7b2-169ad93bdb9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["['He', '', 'o <PERSON>or', 'd']"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["x.split('l')"]}, {"cell_type": "markdown", "id": "328167b5-cc43-467c-bb45-263182093613", "metadata": {}, "source": ["## Print Formatting with Strings"]}, {"cell_type": "code", "execution_count": 44, "id": "de38b899-3c53-4d23-9488-af4e53eff9ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is a String.\n"]}], "source": ["print('This is a {}'.format('String.'))"]}, {"cell_type": "code", "execution_count": 46, "id": "1f780130-54a8-4580-989d-19fc406e4b6e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The quick brown fox\n"]}], "source": ["print('The {2} {0} {1}'.format('brown','fox','quick'))"]}, {"cell_type": "code", "execution_count": 47, "id": "903393ff-d2c6-4a99-8276-ad93a7a0c614", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The quick brown fox\n"]}], "source": ["print('The {q} {b} {f}'.format(b='brown',f='fox',q='quick'))"]}, {"cell_type": "code", "execution_count": 49, "id": "128b5054-876a-445a-b6cc-314a89941c66", "metadata": {}, "outputs": [], "source": ["result = 100/77"]}, {"cell_type": "code", "execution_count": 52, "id": "39b8df3a-ecb5-4a58-b4fd-3226cad939a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The result was 1.299\n"]}], "source": ["print('The result was {r:.3f}'.format(r=result))"]}, {"cell_type": "markdown", "id": "f4ab8dc7-e919-4ce6-8ba6-f2c114d35c80", "metadata": {}, "source": ["#### F string"]}, {"cell_type": "code", "execution_count": 54, "id": "66064fa2-9a93-47bb-9465-8ae17277ff13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello I am <PERSON>!\n"]}], "source": ["print(f'Hello I am {name}!')"]}, {"cell_type": "markdown", "id": "795d53a7-3a3d-4b08-87aa-337d2c4df3ea", "metadata": {}, "source": ["## List"]}, {"cell_type": "code", "execution_count": 67, "id": "cb744fd7-429c-4efa-9ce0-d1edd198cf02", "metadata": {}, "outputs": [], "source": ["my_list = [1,2,3]"]}, {"cell_type": "code", "execution_count": 56, "id": "c82dafcf-9cff-42d9-bb39-a802ec85f68a", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["len(my_list)"]}, {"cell_type": "code", "execution_count": 58, "id": "d7194666-10ca-437c-a45a-59774a0464c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[1]"]}, {"cell_type": "code", "execution_count": 60, "id": "adba4d48-96c8-4064-bdd7-938086f91ef1", "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 3]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[1:]"]}, {"cell_type": "code", "execution_count": 61, "id": "03e10561-05e9-4c77-b4e1-8f601a9459f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["another_list = [4,5]\n", "my_list + another_list"]}, {"cell_type": "code", "execution_count": 68, "id": "e107e59b-3417-41a0-9cd9-580f2ad09e4f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 3]"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[0] = 0\n", "my_list"]}, {"cell_type": "code", "execution_count": 69, "id": "990063e8-1806-475d-8286-b16b7f56decb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 3, 4]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list.append(4)\n", "my_list"]}, {"cell_type": "code", "execution_count": 70, "id": "7aca02bb-18fb-4045-923f-3829ff65ddc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list.pop()"]}, {"cell_type": "code", "execution_count": 72, "id": "e509f9a6-39a2-4b4c-92d0-cbd903f4f84f", "metadata": {}, "outputs": [], "source": ["new_list = [4,2,3,1]"]}, {"cell_type": "code", "execution_count": 73, "id": "a9f62ae5-7aff-49f1-bb22-c2d242aff779", "metadata": {}, "outputs": [], "source": ["new_list.sort()"]}, {"cell_type": "code", "execution_count": 74, "id": "eb9c0562-1a2f-456f-a7d8-6dd5049b5aae", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["new_list"]}, {"cell_type": "code", "execution_count": 75, "id": "2f4d915c-9607-468f-bdc3-ffdade330f51", "metadata": {}, "outputs": [], "source": ["new_list.reverse()"]}, {"cell_type": "code", "execution_count": 76, "id": "6f92cd10-8746-45a7-a416-85c956377d4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[4, 3, 2, 1]"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["new_list"]}, {"cell_type": "markdown", "id": "c2c4dcd7-02af-4f99-9907-48606decb336", "metadata": {}, "source": ["## Dictionaries"]}, {"cell_type": "code", "execution_count": 77, "id": "be376644-d8e4-43ea-9ad6-5ab89f2fbc47", "metadata": {}, "outputs": [], "source": ["my_dict = {1:'Hello',2:'World'}"]}, {"cell_type": "code", "execution_count": 78, "id": "ce23979f-a611-40f6-9f63-c5c0ce74fbd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1: 'Hello', 2: 'World'}"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict"]}, {"cell_type": "code", "execution_count": 79, "id": "adb9023c-5424-4ecd-8511-a844c8ec1598", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello'"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict[1]"]}, {"cell_type": "code", "execution_count": 80, "id": "08152405-68de-4728-a7cb-81d774205629", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys([1, 2])"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict.keys()"]}, {"cell_type": "code", "execution_count": 81, "id": "737306a6-79c9-4202-a4ff-8f73f8235739", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_values(['Hello', 'World'])"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict.values()"]}, {"cell_type": "code", "execution_count": null, "id": "76505585-c265-4810-b328-5456e5faa2f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}