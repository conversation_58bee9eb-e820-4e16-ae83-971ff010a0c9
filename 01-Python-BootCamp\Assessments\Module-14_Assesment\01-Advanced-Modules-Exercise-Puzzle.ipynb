{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../../Pierian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Advanced Modules Exercise Puzzle\n", "\n", "It's time to test your new skills, this puzzle project will combine multiple skills sets, including unzipping files with Python, using os module to automatically search through lots of files.\n", "\n", "## Your Goal\n", "\n", "This is a puzzle, so we don't want to give you too much guidance and instead have you figure out things on your own.\n", "\n", "There is a .zip file called 'unzip_me_for_instructions.zip', unzip it, open the .txt file with Python, read the instructions and see if you can figure out what you need to do!\n", "\n", "**If you get stuck or don't know where to start, here is a [guide/hints](https://docs.google.com/document/d/1JxydUr4n4fSR0EwwuwT-aHia-yPK6r-oTBuVT2sqheo/edit?usp=sharing)**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import shutil"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["shutil.unpack_archive('unzip_me_for_instructions.zip','unzip','zip')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["### I got 5 files , now i have to find for '###-###-####' mobile number in all files "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["content = os.listdir()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['.ipynb_checkpoints', '01-Advanced-Modules-Exercise-Puzzle.ipynb', 'unzip', 'unzip_me_for_instructions.zip']\n"]}], "source": ["print(content)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['extracted_content']\n"]}], "source": ["my_dir_name = \"unzip\"\n", "if os.path.isdir(my_dir_name):\n", "    content = os.listdir(my_dir_name)\n", "    print(content)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["unzip\\extracted_content\n"]}], "source": ["sub_dir_path = os.path.join(my_dir_name,content[0])\n", "print(sub_dir_path)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Five', 'Four', 'Instructions.txt', 'One', 'Three', 'Two']\n"]}], "source": ["content_files = os.listdir(sub_dir_path)\n", "print(content_files)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Instructions.txt'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["content_files.pop(2)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['AEITMYIRQLP.txt', 'APJKSRITGGX.txt', 'AQKATDFGXTS.txt', 'ARLKFCWIAJE.txt', 'AXJGVPVEFAS.txt', 'BNUQEHCFRTG.txt', 'BSKJDRNEZQM.txt', 'BTYWAHLHKBM.txt', 'BUGKBZWRRVI.txt', 'BVBURZZCAPR.txt', 'CAHBEVSVDDN.txt', 'COMGMZBJAYE.txt', 'CRFSDGYFSHA.txt', 'CSCLFZCDYYC.txt', 'CXBVCTRBBIE.txt', 'DDLASODUVPX.txt', 'DHZBAAYEADM.txt', 'DQPZQLBCJYP.txt', 'DYOPIIVMZOO.txt', 'DZUWWXYIAEL.txt', 'EAAOEPSAWMQ.txt', 'EIPWXMQZJKU.txt', 'ESIZWBHMGDP.txt', 'ETCUEXWNBCF.txt', 'EYTCGIOYWIW.txt']\n", "------------------\n", "['ECIOBYCDVFI.txt', 'EMTGPSXQEJX.txt', 'EPRNUHRSESC.txt', 'ESDIZXHYCVY.txt', 'EXVQSVBQQQH.txt', 'QCTCKDIBBVG.txt', 'QCWCFLKNZMN.txt', 'QDDETWBHJYC.txt', 'QTDYYIFPHAU.txt', 'QVNJULGXNUM.txt', 'REAXWSOIQDY.txt', 'ROICPTWKXDX.txt', 'RSXOTNGKBML.txt', 'RXDARIDGKBF.txt', 'RYNXFYXMKHG.txt', 'TAKNAVDMZKV.txt', 'THPNEGKTJWI.txt', 'TJFMLJODVAD.txt', 'TKCZSFQNJTX.txt', 'TWUOYFCCYBQ.txt', 'WFSKPTXPFCH.txt', 'WHTOHQUWXIN.txt', 'WNJISWPEBRS.txt', 'WXDJDOGZEHN.txt', 'WYDLGSGGXKV.txt']\n", "------------------\n", "['HDOHZHFSTTK.txt', 'HFUTPPAXDIS.txt', 'HMNZTLIFGPD.txt', 'HRQFTHKVJTL.txt', 'HVUTZEVMSBW.txt', 'JDLRVFCXYLU.txt', 'JEHBLZPUPSP.txt', 'JLTXKIGCWDL.txt', 'JQUOBKFUACN.txt', 'JTHSNBNPQSE.txt', 'KCXGNQCZBLO.txt', 'KFIUZFERLET.txt', 'KMMLGJOWLGI.txt', 'KNBSKDREHQU.txt', 'KTXDHIOKAUI.txt', 'LDGOCUQJNNS.txt', 'LFEATJAAYDC.txt', 'LHODFIKVTQA.txt', 'LIFDHOFKWOI.txt', 'LPNDVDXPZIG.txt', 'PDJMSMNKIRM.txt', 'PHWAVPEKAER.txt', 'PLYCGPVEAWO.txt', 'PQNVCVJINAR.txt', 'PTOBBCJYURJ.txt']\n", "------------------\n", "['VAQIJTDOFUJ.txt', 'VCFJCGJFBIH.txt', 'VSXFSTABZDY.txt', 'VVHFVZUNLOO.txt', 'VVKGWLRMHLU.txt', 'XAJMCPEWFNI.txt', 'XFHJOTNPEJG.txt', 'XHZPVUQTXIO.txt', 'XJCWENFFGHB.txt', 'XVMPVSVYKFR.txt', 'YCESZHJDBXH.txt', 'YDQFMWXOUMW.txt', 'YQBIUHSUEVW.txt', 'YQRNCMNFFHW.txt', 'YYIZGBTQHZP.txt', 'ZEZKKRBIZEB.txt', 'ZKQJXAYKPVD.txt', 'ZOWVXWPOGWP.txt', 'ZXEZRQXZNPG.txt', 'ZXIBJMPROKW.txt']\n", "------------------\n", "['GKQBQRCTNNK.txt', 'GMMQQUBMJNR.txt', 'GQTJJORZBXY.txt', 'GTOTCIWMDBY.txt', 'GXYSEPAFRTP.txt', 'HARDNJGDRBC.txt', 'HEORIXOTANT.txt', 'HMUTDOVNYTV.txt', 'HMZXPBOPRAE.txt', 'HTOHSTYXTCO.txt', 'LCJZYDHBFRM.txt', 'LSQSTGPIGIY.txt', 'LULTNYAQEJG.txt', 'LVMBINRBJXL.txt', 'LYZEQCVYNEZ.txt', 'OHZOUOSFJQC.txt', 'OIHMLGMWTHL.txt', 'OKWFOOYTXFU.txt', 'OMWIMVRCMYM.txt', 'OYMAGXAGWHJ.txt', 'SIKFPPLCJDN.txt', 'SJMJLDGPBSJ.txt', 'SOFUJYXTIMK.txt', 'SPDZYGDHEWO.txt', 'SWOFXREEH<PERSON>.txt']\n", "------------------\n"]}], "source": ["for i in content_files:\n", "    all_files = os.listdir(sub_dir_path+\"\\\\\"+i)\n", "    print(all_files)\n", "    print('------------------')\n", "    "]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["['************']"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["total_traced_outcomes = []\n", "for i in content_files:\n", "    all_files = os.listdir(sub_dir_path+\"\\\\\"+i)\n", "    for j in all_files:\n", "        f = open(sub_dir_path+\"\\\\\"+i+\"\\\\\"+j)\n", "        content = f.read()\n", "        total_traced_outcomes.extend(re.findall(r'\\d{3}-\\d{3}-\\d{4}',content))\n", "\n", "total_traced_outcomes       "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}