{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# Statements Assessment Test\n", "Let's test your knowledge!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["_____\n", "**Use <code>for</code>, .split(), and <code>if</code> to create a Statement that will print out words that start with 's':**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["st = 'Print only the words that start with s in this sentence'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start\n", "s\n", "sentence\n"]}], "source": ["#Code here\n", "list_of_words = st.split()\n", "for word in list_of_words:\n", "    if word.startswith('s'):\n", "        print(word)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["______\n", "**Use range() to print all the even numbers from 0 to 10.**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8, 10]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#Code Here\n", "list(range(0,11,2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["___\n", "**Use a List Comprehension to create a list of all numbers between 1 and 50 that are divisible by 3.**"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["[3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#Code in this cell\n", "list1 = [num for num in range(1,51) if num % 3 == 0]\n", "list1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["_____\n", "**Go through the string below and if the length of a word is even print \"even!\"**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["st = 'Print every word in this sentence that has an even number of letters'"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["only\n", "that\n", "with\n", "in\n", "this\n", "sentence\n"]}], "source": ["#Code in this cell\n", "\n", "for word in st.split():\n", "    if len(word) % 2 == 0:\n", "        print(word)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Write a program that prints the integers from 1 to 100. But for multiples of three print \"Fizz\" instead of the number, and for the multiples of five print \"Buzz\". For numbers which are multiples of both three and five print \"FizzBuzz\".**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "Fizz\n", "4\n", "Buzz\n", "Fizz\n", "7\n", "8\n", "Fizz\n", "Buzz\n", "11\n", "Fizz\n", "13\n", "14\n", "FizzBuzz\n", "16\n", "17\n", "Fizz\n", "19\n", "Buzz\n", "Fizz\n", "22\n", "23\n", "Fizz\n", "Buzz\n", "26\n", "Fizz\n", "28\n", "29\n", "FizzBuzz\n", "31\n", "32\n", "Fizz\n", "34\n", "Buzz\n", "Fizz\n", "37\n", "38\n", "Fizz\n", "Buzz\n", "41\n", "Fizz\n", "43\n", "44\n", "FizzBuzz\n", "46\n", "47\n", "Fizz\n", "49\n", "Buzz\n", "Fizz\n", "52\n", "53\n", "Fizz\n", "Buzz\n", "56\n", "Fizz\n", "58\n", "59\n", "FizzBuzz\n", "61\n", "62\n", "Fizz\n", "64\n", "Buzz\n", "Fizz\n", "67\n", "68\n", "Fizz\n", "Buzz\n", "71\n", "Fizz\n", "73\n", "74\n", "FizzBuzz\n", "76\n", "77\n", "Fizz\n", "79\n", "Buzz\n", "Fizz\n", "82\n", "83\n", "Fizz\n", "Buzz\n", "86\n", "Fizz\n", "88\n", "89\n", "FizzBuzz\n", "91\n", "92\n", "Fizz\n", "94\n", "Buzz\n", "Fizz\n", "97\n", "98\n", "Fizz\n", "Buzz\n"]}], "source": ["#Code in this cell\n", "for i in range(1,101):\n", "    if i % 3 == 0 and i % 5 == 0:\n", "        print('FizzBuzz')\n", "    elif i % 3 == 0:\n", "        print(\"Fizz\")\n", "    elif i % 5 == 0:\n", "        print(\"Buzz\")\n", "    else:\n", "        print(i)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["____\n", "**Use List Comprehension to create a list of the first letters of every word in the string below:**"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["st = 'Create a list of the first letters of every word in this string'"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["['C', 'a', 'l', 'o', 't', 'f', 'l', 'o', 'e', 'w', 'i', 't', 's']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["#Code in this cell\n", "list4 = [word[0] for word in st.split()]\n", "list4"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 1}