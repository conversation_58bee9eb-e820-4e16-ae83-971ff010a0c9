{"cells": [{"cell_type": "markdown", "id": "837ef1df", "metadata": {}, "source": ["# Task: Implement a Decorator to Measure Memory Usage of a Function\n", "\n", "## Problem Statement:\n", "Write a Python program that implements a **decorator** to measure the **memory usage** of a given function. The goal is to analyze how much additional memory is consumed during the execution of the function.\n", "\n", "## Steps:\n", "1. Import the `tracemalloc` module, which tracks memory allocations in Python.\n", "2. Define a decorator function that wraps any target function.\n", "3. In the wrapper:\n", "   - Start memory tracking with `tracemalloc.start()`.\n", "   - Call the target function and store the result.\n", "   - Get memory usage statistics using `tracemalloc.get_traced_memory()`.\n", "   - Stop tracking with `tracemalloc.stop()`.\n", "   - Print the memory usage details.\n", "4. Return the result of the target function.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f4c87684", "metadata": {}, "outputs": [], "source": ["import tracemalloc\n", "import functools"]}, {"cell_type": "code", "execution_count": 2, "id": "0ac960d7", "metadata": {}, "outputs": [], "source": ["def measure_memory_usage(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        tracemalloc.start()\n", "        result = func(*args, **kwargs)\n", "        current, peak = tracemalloc.get_traced_memory()\n", "        tracemalloc.stop()\n", "        print(f\"Function: {func.__name__}\")\n", "        print(f\"Memory Usage: {current / 1024 / 1024:.2f} MB\")\n", "        print(f\"Peak Memory Usage: {peak / 1024 / 1024:.2f} MB\")\n", "        return result\n", "    return wrapper"]}, {"cell_type": "code", "execution_count": 3, "id": "168fabee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function: generate_large_list\n", "Memory Usage: 38.72 MB\n", "Peak Memory Usage: 38.72 MB\n"]}], "source": ["@measure_memory_usage\n", "def generate_large_list():\n", "    return [i ** 2 for i in range(10**6)]\n", "\n", "test = generate_large_list()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}