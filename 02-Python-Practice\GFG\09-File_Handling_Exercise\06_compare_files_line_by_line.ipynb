{"cells": [{"cell_type": "markdown", "id": "cd576a7c", "metadata": {}, "source": ["# Task: Compare Two Files Line by Line in Python\n", "\n", "## Problem Statement:\n", "Write a Python program to compare two text files line by line and display lines that differ. This can help in debugging, content verification, or simply syncing data between two files.\n", "\n", "## Steps:\n", "1. **Open both files** using the `open()` function in read mode.\n", "2. **Read lines** from both files using `readlines()` or loop line by line.\n", "3. **Compare corresponding lines** from both files.\n", "4. **Print differing lines** with the line number (optional for clarity).\n", "5. **Handle unequal file lengths**, if necessary."]}, {"cell_type": "code", "execution_count": 1, "id": "856226e3", "metadata": {}, "outputs": [], "source": ["file1_path = 'input.txt'\n", "file2_path = 'output.txt'"]}, {"cell_type": "code", "execution_count": 2, "id": "d1116b25", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Comparing files:\n", " @ input.txt\n", " # output.txt\n", "\n"]}], "source": ["print(\"Comparing files:\")\n", "print(f\" @ {file1_path}\")\n", "print(f\" # {file2_path}\")\n", "print()"]}, {"cell_type": "code", "execution_count": 3, "id": "99f7868e", "metadata": {}, "outputs": [], "source": ["with open(file1_path,'r') as f1, open(file2_path,'r') as f2:\n", "    common_lines = set(f1).intersection(set(f2))"]}, {"cell_type": "code", "execution_count": 4, "id": "dea1466e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Common Lines in both Files:\n", "\n", "Special cases aren't special enough to break the rules.\n", "Explicit is better than implicit.\n", "Unless explicitly silenced.Although practicality beats purity.\n", "Readability counts.\n", "Errors should never pass silently.\n"]}], "source": ["print(\"Common Lines in both Files:\\n\")\n", "for line in common_lines:\n", "        print(line,end='')"]}, {"cell_type": "code", "execution_count": 5, "id": "4809c5a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Difference Lines in Both Files:\n", "\n", "Line 2:\n", "\n", "@ Simple is better than complex.\n", "# Geeksforgeeks Is A Computer Science Portal For Geeks.\n", "\n", "Line 3:\n", "\n", "@ Complex is better than complicated.\n", "# Readability counts.\n", "\n", "Line 4:\n", "\n", "@ Flat is better than nested.\n", "# It Contains Well Written, Well Thought And Well Explained\n", "\n", "Line 5:\n", "\n", "@ Sparse is better than dense.\n", "# Special cases aren't special enough to break the rules.\n", "\n", "Line 6:\n", "\n", "@ Readability counts.\n", "# Although practicality beats purity.\n", "\n", "Line 7:\n", "\n", "@ Special cases aren't special enough to break the rules.\n", "# Computer Science And Programming Articles, Quizzes Etc.\n", "\n", "Line 8:\n", "\n", "@ Although practicality beats purity.\n", "# Errors should never pass silently.\n", "\n", "Line 9:\n", "\n", "@ Errors should never pass silently.\n", "# Unless explicitly silenced.\n", "\n", "Line 10:\n", "\n", "@ Unless explicitly silenced.\n", "# \n", "\n"]}], "source": ["print(\"\\n\\nDifference Lines in Both Files:\\n\")\n", "\n", "with open(file1_path,'r') as f1, open(file2_path,'r') as f2:\n", "    line_no = 1\n", "    while True:\n", "        line1 = f1.readline()\n", "        line2 = f2.readline()\n", "\n", "        if not line1 and not line1:\n", "            break\n", "\n", "        line1 = line1.strip()\n", "        line2 = line2.strip()\n", "\n", "        if line1 != line2:\n", "            print(f\"Line {line_no}:\\n\")\n", "            print(f\"@ {line1}\")\n", "            print(f\"# {line2}\\n\")\n", "        line_no += 1"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}