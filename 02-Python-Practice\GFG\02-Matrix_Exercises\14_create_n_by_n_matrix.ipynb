{"cells": [{"cell_type": "markdown", "id": "f0defdf8-13b2-4537-9b12-584bac05d8ca", "metadata": {}, "source": ["# Task: Matrix Creation of n×n\n", "\n", "## Problem Statement:\n", "Create an n×n matrix in Python using nested list comprehension.\n", "\n", "### Steps:\n", "1. Use an outer list comprehension to iterate `n` times for each row.\n", "2. Within it, use an inner list comprehension to generate `n` elements for each row.\n", "3. Populate each element with a default value or pattern as needed.\n", "4. Return the constructed n×n matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "cd3b36b5-a2cd-46db-ae2d-9035ded40da4", "metadata": {}, "outputs": [], "source": ["n = 4"]}, {"cell_type": "code", "execution_count": 2, "id": "f9f1d3fa-db5d-495d-b09e-95bba32881b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]]\n"]}], "source": ["matrix = [[0 for _ in range(n)] for _ in range(n)]\n", "print(matrix)"]}, {"cell_type": "code", "execution_count": 3, "id": "66bd327e-5c1e-4311-8d75-cb9c1efaffcc", "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 4, "id": "8d1149a4-0ec6-48dc-8b48-ea1c4be9abe5", "metadata": {}, "outputs": [], "source": ["n = 5"]}, {"cell_type": "code", "execution_count": 5, "id": "827d4cf0-412c-47b0-81ca-0bcd99fa05db", "metadata": {}, "outputs": [], "source": ["matrix = [[random.randint(0, 9) for _ in range(n)] for _ in range(n)]"]}, {"cell_type": "code", "execution_count": 6, "id": "f26513d7-b9ad-437c-9ad6-c0f399db261c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Random matrix:\n", "[1, 2, 5, 9, 5]\n", "[7, 1, 8, 4, 1]\n", "[7, 1, 2, 2, 4]\n", "[8, 1, 0, 2, 5]\n", "[8, 2, 5, 4, 0]\n"]}], "source": ["print(\"Random matrix:\")\n", "for row in matrix:\n", "    print(row)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}