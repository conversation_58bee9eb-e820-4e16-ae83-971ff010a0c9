{"cells": [{"cell_type": "markdown", "id": "6113e421", "metadata": {}, "source": ["# Task: Create a New JSON File from an Existing JSON File\n", "\n", "## Problem Statement:\n", "Write a Python program that reads data from an existing JSON file and writes it into a new JSON file. This can be useful for data migration, transformation, or simply creating backups.\n", "\n", "## Steps:\n", "1. Import the `json` module.\n", "2. Use `open()` and `json.load()` to read and parse the existing JSON file.\n", "3. Use `open()` and `json.dump()` to write the parsed data into a new JSON file.\n", "4. Optionally, format the output using `indent` for readability.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c431144a", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 2, "id": "8a74cff7", "metadata": {}, "outputs": [], "source": ["with open('file1.json','r') as input:\n", "    data = json.load(input)"]}, {"cell_type": "code", "execution_count": 3, "id": "46fe2cf6", "metadata": {}, "outputs": [], "source": ["with open('output.json','w') as output:\n", "    json.dump(data, output, indent=4)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}