{"cells": [{"cell_type": "markdown", "id": "777c9913-ac9c-4f5a-951b-b2df3db8230c", "metadata": {}, "source": ["# Task: Group Similar Elements into Matrix\n", "\n", "## Problem Statement:\n", "Given a matrix (2D list), group all similar elements together into sublists based on their value.\n", "\n", "### Steps:\n", "1. Flatten the matrix into a single list of elements.\n", "2. Sort the flattened list to prepare it for grouping.\n", "3. Use `itertools.groupby()` to group identical elements together.\n", "4. Use list comprehension to iterate over the grouped data and collect groups into sublists.\n", "5. Return the final list of grouped sublists.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "598e448a-cd93-4a35-813e-8b3a331715cb", "metadata": {}, "outputs": [], "source": ["from itertools import groupby"]}, {"cell_type": "code", "execution_count": 2, "id": "150fbf93-8f00-449a-b9a1-2504a8bf1e29", "metadata": {}, "outputs": [], "source": ["def group_similar_elements(test_list):\n", "    return [list(val) for key, val in groupby(sorted(test_list))]"]}, {"cell_type": "code", "execution_count": 3, "id": "aebd3714-f9b1-42a3-b611-dd9441185eb2", "metadata": {}, "outputs": [], "source": ["test_list = [1, 3, 5, 1, 3, 2, 5, 4, 2]"]}, {"cell_type": "code", "execution_count": 4, "id": "f021a481-7394-4105-90bf-82efb779f5b0", "metadata": {}, "outputs": [], "source": ["result = group_similar_elements(test_list)"]}, {"cell_type": "code", "execution_count": 5, "id": "9cc5d4b0-f305-4b91-8aa2-aad7860c5b22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matrix after grouping: [[1, 1], [2, 2], [3, 3], [4], [5, 5]]\n"]}], "source": ["print(\"Matrix after grouping:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}