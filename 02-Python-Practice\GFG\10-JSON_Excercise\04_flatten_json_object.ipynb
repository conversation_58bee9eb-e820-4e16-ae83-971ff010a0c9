{"cells": [{"cell_type": "markdown", "id": "1fdff7cd", "metadata": {}, "source": ["# Task: Flattening JSON Objects in Python\n", "\n", "## Problem Statement:\n", "Given a nested JSON object (with dictionaries and/or lists as values), the goal is to flatten it into a single-level dictionary with compound keys indicating the original nesting structure.\n", "\n", "## Steps:\n", "1. **Import required libraries**, typically `json` and optionally `pandas` or write a recursive function.\n", "2. **Parse the JSON data** into a Python dictionary using `json.loads()` or `json.load()`.\n", "3. **Use a recursive function or `pandas.json_normalize()`** to flatten nested structures.\n", "4. **Store or print** the resulting flat dictionary or DataFrame."]}, {"cell_type": "markdown", "id": "16635413", "metadata": {}, "source": ["## Approach 1: Recursive Approach"]}, {"cell_type": "code", "execution_count": 1, "id": "b5a7a88b", "metadata": {}, "outputs": [], "source": ["unflat_json = {\n", "    'user': {\n", "        'Rachel': {\n", "            'UserID': 1717171717,\n", "            'Email': '<EMAIL>',\n", "            'friends': ['<PERSON>', '<PERSON>', '<PERSON>']\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 2, "id": "48fafa07", "metadata": {}, "outputs": [], "source": ["def flatten_json(data):\n", "    flat_dict = {}\n", "\n", "    def _flatten(obj, parent_key=''):\n", "        if isinstance(obj, dict):\n", "            for key, value in obj.items():\n", "                _flatten(value, f\"{parent_key}{key}_\")\n", "        elif isinstance(obj, list):\n", "            for i, item in enumerate(obj):\n", "                _flatten(item, f\"{parent_key}{i}_\")\n", "        else:\n", "            flat_dict[parent_key[:-1]] = obj\n", "\n", "    _flatten(data)\n", "    return flat_dict"]}, {"cell_type": "code", "execution_count": 3, "id": "e45cd690", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'user_<PERSON>_UserID': 1717171717, 'user_<PERSON>_<PERSON>': '<EMAIL>', 'user_<PERSON>_friends_0': '<PERSON>', 'user_<PERSON>_friends_1': '<PERSON>', 'user_<PERSON>_friends_2': '<PERSON>'}\n"]}], "source": ["flattened = flatten_json(unflat_json)\n", "print(flattened)"]}, {"cell_type": "markdown", "id": "869a9376", "metadata": {}, "source": ["## Approach 2: Using flatten_json library"]}, {"cell_type": "code", "execution_count": 5, "id": "5c13e98c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'user_<PERSON>_UserID': 1717171717, 'user_<PERSON>_<PERSON>': '<EMAIL>', 'user_<PERSON>_friends_0': '<PERSON>', 'user_<PERSON>_friends_1': '<PERSON>', 'user_<PERSON>_friends_2': '<PERSON>'}\n"]}], "source": ["from flatten_json import flatten\n", "\n", "flat_json = flatten(unflat_json)\n", "\n", "print(flat_json)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}