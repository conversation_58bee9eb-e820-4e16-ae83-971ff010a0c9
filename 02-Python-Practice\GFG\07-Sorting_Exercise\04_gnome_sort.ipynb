{"cells": [{"cell_type": "markdown", "id": "104da4ba", "metadata": {}, "source": ["# Task: Python Program for Gnome Sort\n", "\n", "## Problem Statement:\n", "Implement the **Gnome Sort** algorithm, which sorts an array by repeatedly swapping elements that are out of order and stepping backwards until the order is correct.\n", "\n", "## Steps:\n", "1. Start from the first index `i = 0`.\n", "2. If `i == 0`, move to the next index (`i += 1`).\n", "3. If `arr[i] >= arr[i - 1]`, move one step forward (`i += 1`).\n", "4. If `arr[i] < arr[i - 1]`, swap `arr[i]` and `arr[i - 1]`, then step one step back (`i -= 1`).\n", "5. Repeat steps 2 to 4 until `i` reaches the end of the array.\n", "6. The array is now sorted."]}, {"cell_type": "code", "execution_count": 1, "id": "87376a52", "metadata": {}, "outputs": [], "source": ["def gnomeSort(arr, n):\n", "    index = 0\n", "    while index < n:\n", "        if index == 0:\n", "            index += 1\n", "        if arr[index] >= arr[index - 1]:\n", "            index += 1\n", "        else:\n", "            arr[index], arr[index - 1] = arr[index - 1], arr[index]\n", "            index -= 1\n", "    return arr"]}, {"cell_type": "code", "execution_count": 2, "id": "0e4cad20", "metadata": {}, "outputs": [], "source": ["arr = [34, 2, 10, -9]"]}, {"cell_type": "code", "execution_count": 3, "id": "8e73ebab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-9, 2, 10, 34]\n"]}], "source": ["print(gnomeSort(arr, len(arr)))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}