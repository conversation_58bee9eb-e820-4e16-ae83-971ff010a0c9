{"cells": [{"cell_type": "markdown", "id": "6aa983b5", "metadata": {}, "source": ["# Task: Interval Intersection using Pendulum\n", "\n", "## Problem Statement:\n", "Write a Python program to **find the overlapping (intersection) period** between two given **time intervals** using the `pendulum` module.\n", "\n", "## Steps:\n", "1. **Import** the `pendulum` module.\n", "2. Define the **start and end times** of both intervals using `pendulum.parse()` or `pendulum.datetime()`.\n", "3. Use `max(start1, start2)` to find the **latest start time**.\n", "4. Use `min(end1, end2)` to find the **earliest end time**.\n", "5. If the **latest start time is earlier than or equal to the earliest end time**, an overlap exists.\n", "6. **Print the overlapping interval** or indicate that there is no intersection.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "96f039fc", "metadata": {}, "outputs": [], "source": ["import pendulum"]}, {"cell_type": "code", "execution_count": 2, "id": "ce4c1af7", "metadata": {}, "outputs": [], "source": ["interval1_start = \"2020-03-15T09:00:00\"\n", "interval1_end = \"2020-03-15T11:00:00\"\n", "\n", "interval2_start = \"2020-03-15T10:00:00\"\n", "interval2_end = \"2020-03-15T13:00:00\""]}, {"cell_type": "code", "execution_count": 3, "id": "4da30d04", "metadata": {}, "outputs": [], "source": ["interval1_start_time = pendulum.parse(interval1_start)\n", "interval1_end_time = pendulum.parse(interval1_end)\n", "interval2_start_time = pendulum.parse(interval2_start)\n", "interval2_end_time = pendulum.parse(interval2_end)"]}, {"cell_type": "code", "execution_count": 4, "id": "6ffef2b2", "metadata": {}, "outputs": [], "source": ["overlap_start = max(interval1_start_time, interval2_start_time)\n", "overlap_end = min(interval1_end_time, interval2_end_time)"]}, {"cell_type": "code", "execution_count": 5, "id": "a0e88426", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There is an overlapping period from 2020-03-15 10:00:00+00:00 to 2020-03-15 11:00:00+00:00.\n"]}], "source": ["if overlap_start <= overlap_end:\n", "    print(f\"There is an overlapping period from {overlap_start} to {overlap_end}.\")\n", "else:\n", "    print(\"There is no overlapping period.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}