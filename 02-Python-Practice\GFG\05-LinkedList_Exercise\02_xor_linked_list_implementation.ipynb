{"cells": [{"cell_type": "markdown", "id": "94262e89-d01a-4e7d-aea1-8bb6a5621e97", "metadata": {}, "source": ["# Task: Implementation of XOR Linked List in Python\n", "\n", "## Problem Statement:\n", "Create a memory-efficient version of a doubly linked list using the XOR Linked List technique in Python. This structure stores the XOR of the previous and next node addresses to reduce memory usage. However, due to Python's garbage collection and lack of direct memory address manipulation, this implementation serves more as a conceptual demonstration than a practical solution.\n", "\n", "## Steps:\n", "1. Define a `Node` class with attributes for data and a pointer `npx` (XOR of next and previous node references).\n", "2. Define the `XORLinkedList` class to manage operations.\n", "3. Implement the following methods:\n", "   - `InsertAtStart()`: Insert node at the beginning.\n", "   - `InsertAtEnd()`: Insert node at the end.\n", "   - `DeleteAtStart()`: Delete node at the beginning.\n", "   - `DeleteAtEnd()`: Delete node at the end.\n", "   - `Print()`: Traverse and print list from start to end.\n", "   - `ReversePrint()`: Traverse and print list from end to start.\n", "   - `Length()`: Return total number of nodes in the list.\n", "   - `PrintByIndex()`: Get data by index from the list.\n", "   - `isEmpty()`: Check whether the list is empty.\n", "   - `__type_cast()`: Perform type casting to simulate memory address behavior.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8b1eae71-530d-4ef0-86a8-c6171e94bf91", "metadata": {}, "outputs": [], "source": ["import ctypes"]}, {"cell_type": "code", "execution_count": 2, "id": "60eb7abf-ebcc-4320-9b07-ca0bfe02cc78", "metadata": {}, "outputs": [], "source": ["class Node:\n", "    def __init__(self,value):\n", "        self.value = value\n", "        self.npx = 0"]}, {"cell_type": "code", "execution_count": 3, "id": "5db8ddaa-a722-4b96-9b60-992b23cf9ba5", "metadata": {}, "outputs": [], "source": ["class XorLinkedList:\n", "\n", "    def __init__(self):\n", "        self.head = None\n", "        self.tail = None\n", "        self.__nodes = []\n", "\n", "    def InsertAtStart(self, value):\n", "        node = Node(value)\n", "        if self.head is None:\n", "            self.head = node\n", "            self.tail = node\n", "        else:\n", "            self.head.npx = id(node) ^ self.head.npx\n", "            node.npx = id(self.head)\n", "            self.head = node\n", "        self.__nodes.append(node)\n", "\n", "    def InsertAtEnd(self, value):\n", "        node = Node(value)\n", "        if self.head is None:\n", "            self.head = node\n", "            self.tail = node\n", "        else:\n", "            self.tail.npx = id(node) ^ self.tail.npx\n", "            node.npx = id(self.tail)\n", "            self.tail = node\n", "        self.__nodes.append(node)\n", "\n", "    def Delete<PERSON>tStart(self):\n", "        if self.isEmpty():\n", "            return \"List is Empty!\"\n", "        elif self.head == self.tail:\n", "            self.head = self.tail = None\n", "        elif (0 ^ self.head.npx) == id(self.tail):\n", "            self.head = self.tail\n", "            self.head.npx = self.tail.npx = 0\n", "        else:\n", "            res = self.head.value\n", "            x = self.__type_cast(0 ^ self.head.npx)\n", "            y = id(self.head) ^ x.npx\n", "            self.head = x\n", "            self.head.npx = 0 ^ y\n", "            return res\n", "\n", "    def DeleteAtEnd(self):\n", "        if self.isEmpty():\n", "            return \"List is empty !\"\n", "        elif self.head == self.tail:\n", "            self.head = self.tail = None\n", "        elif self.__type_cast(0 ^ self.head.npx) == self.tail:\n", "            self.tail = self.head\n", "            self.head.npx = self.tail.npx = 0\n", "        else:\n", "            prev_id = 0\n", "            node = self.head\n", "            next_id = 1\n", "            while next_id:\n", "                next_id = prev_id ^ node.npx\n", "                if next_id:\n", "                    prev_id = id(node)\n", "                    node = self.__type_cast(next_id)\n", "            res = node.value\n", "            x = self.__type_cast(prev_id).npx ^ id(node)\n", "            y = self.__type_cast(prev_id)\n", "            y.npx = x ^ 0\n", "            self.tail = y\n", "            return res\n", "\n", "    def Print(self):\n", "        if self.head != None:\n", "            prev_id = 0\n", "            node = self.head\n", "            next_id = 1\n", "            print(node.value, end=\" \")\n", "            while next_id:\n", "                next_id = prev_id ^ node.npx\n", "                if next_id:\n", "                    prev_id = id(node)\n", "                    node = self.__type_cast(next_id)\n", "                    print(node.value, end=\" \")\n", "                else:\n", "                    return\n", "        else:\n", "            print(\"List is empty !\")\n", "\n", "    def <PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        if self.head != None:\n", "            prev_id = 0\n", "            node = self.tail\n", "            next_id = 1\n", "            print(node.value, end=\" \")\n", "            while next_id:\n", "                next_id = prev_id ^ node.npx\n", "                if next_id:\n", "                    prev_id = id(node)\n", "                    node = self.__type_cast(next_id)\n", "                    print(node.value, end=\" \")\n", "                else:\n", "                    return\n", "        else:\n", "            print(\"List is empty !\")\n", "\n", "    def Length(self):\n", "        if not self.isEmpty():\n", "            prev_id = 0\n", "            node = self.head\n", "            next_id = 1\n", "            count = 1\n", "            while next_id:\n", "                next_id = prev_id ^ node.npx\n", "                if next_id:\n", "                    prev_id = id(node)\n", "                    node = self.__type_cast(next_id)\n", "                    count += 1\n", "                else:\n", "                    return count\n", "        else:\n", "            return 0\n", "\n", "    def PrintByIndex(self, index):\n", "        prev_id = 0\n", "        node = self.head\n", "        for i in range(index):\n", "            next_id = prev_id ^ node.npx\n", "            if next_id:\n", "                prev_id = id(node)\n", "                node = self.__type_cast(next_id)\n", "            else:\n", "                return \"Value doesn't found index out of range.\"\n", "        return node.value\n", "\n", "    def is<PERSON><PERSON>y(self):\n", "        if self.head is None:\n", "            return True\n", "        return False\n", "\n", "    def __type_cast(self, id):\n", "        return ctypes.cast(id, ctypes.py_object).value"]}, {"cell_type": "code", "execution_count": 4, "id": "b9f10a55", "metadata": {}, "outputs": [], "source": ["obj = XorLinkedList()\n", "obj.InsertAtEnd(2)\n", "obj.InsertAtEnd(3)\n", "obj.InsertAtEnd(4)\n", "obj.InsertAtStart(0)\n", "obj.<PERSON>sertAtStart(6)\n", "obj.<PERSON>sertAt<PERSON>nd(55)"]}, {"cell_type": "code", "execution_count": 5, "id": "fe38e867", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Length: 6\n"]}], "source": ["print(\"\\nLength:\", obj.Length())"]}, {"cell_type": "code", "execution_count": 6, "id": "e19bc63f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Traverse linked list:\n", "6 0 2 3 4 55 "]}], "source": ["print(\"\\nTraverse linked list:\")\n", "obj.Print()"]}, {"cell_type": "code", "execution_count": 7, "id": "bc8ca6c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Traverse in reverse order:\n", "55 4 3 2 0 6 "]}], "source": ["print(\"\\nTraverse in reverse order:\")\n", "obj.<PERSON><PERSON><PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 8, "id": "e329ee3c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Nodes:\n", "Data value at index 0 is 6\n", "Data value at index 1 is 0\n", "Data value at index 2 is 2\n", "Data value at index 3 is 3\n", "Data value at index 4 is 4\n", "Data value at index 5 is 55\n"]}], "source": ["print('\\nNodes:')\n", "for i in range(obj.Length()):\n", "    print(\"Data value at index\", i, 'is', obj.PrintByIndex(i))"]}, {"cell_type": "code", "execution_count": 9, "id": "0a2bd8f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Delete Last Node:  55\n", "\n", "Delete First Node:  6\n"]}], "source": ["print(\"\\nDelete Last Node: \", obj.DeleteAtEnd())\n", "print(\"\\nDelete First Node: \", obj.DeleteAtStart())"]}, {"cell_type": "code", "execution_count": 10, "id": "775a4395", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Updated length: 4\n"]}], "source": ["print(\"\\nUpdated length:\", obj.Length())"]}, {"cell_type": "code", "execution_count": 11, "id": "bb7cbd3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Nodes:\n", "Data value at index 0 is 0\n", "Data value at index 1 is 2\n", "Data value at index 2 is 3\n", "Data value at index 3 is 4\n"]}], "source": ["print('\\nNodes:')\n", "for i in range(obj.Length()):\n", "    print(\"Data value at index\", i, 'is', obj.PrintByIndex(i))"]}, {"cell_type": "code", "execution_count": 12, "id": "1f303527", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Traverse linked list:\n", "0 2 3 4 "]}], "source": ["print(\"\\nTraverse linked list:\")\n", "obj.Print()"]}, {"cell_type": "code", "execution_count": 13, "id": "b1ec43cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Traverse in reverse order:\n", "4 3 2 0 "]}], "source": ["print(\"\\nTraverse in reverse order:\")\n", "obj.<PERSON><PERSON><PERSON><PERSON><PERSON>()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}