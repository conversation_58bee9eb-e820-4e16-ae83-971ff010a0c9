{"cells": [{"cell_type": "markdown", "id": "b822a1e1", "metadata": {}, "source": ["# Task: Sundays Selector\n", "\n", "## Problem Statement:\n", "Write a Python program to **find and list all Sundays in a given year**. This can be useful for scheduling events, creating calendars, or doing date-based data analysis.\n", "\n", "## Steps:\n", "1. **Import** the `datetime` module.\n", "2. **Take year as input** from the user or define it in the program.\n", "3. <PERSON> through all the months (1 to 12).\n", "4. For each month, loop through all days and:\n", "   - Check if the day is a **Sunday** using `weekday()` (where Sunday = 6).\n", "5. If the day is Sunday, **add it to a list**.\n", "6. Finally, **print all the Sundays** in the format `YYYY-MM-DD`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "74bc9fdf", "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": 2, "id": "187cc2fa", "metadata": {}, "outputs": [], "source": ["def find_sundays(year):\n", "    sundays = []\n", "\n", "    dt = datetime.date(year, 1, 1)\n", "\n", "    while dt.year == year:\n", "        if dt.weekday() == 6:\n", "            sundays.append(dt)\n", "        dt += datetime.<PERSON><PERSON><PERSON>(days=1)\n", "    return sundays"]}, {"cell_type": "code", "execution_count": 3, "id": "5f5e384e", "metadata": {}, "outputs": [], "source": ["year = int(input(\"Enter the year: \"))"]}, {"cell_type": "code", "execution_count": 4, "id": "a4d759a3", "metadata": {}, "outputs": [], "source": ["sundays = find_sundays(year)"]}, {"cell_type": "code", "execution_count": 5, "id": "fa1f72a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "All Sundays in 2025:\n", "05-01-2025\n", "12-01-2025\n", "19-01-2025\n", "26-01-2025\n", "02-02-2025\n", "09-02-2025\n", "16-02-2025\n", "23-02-2025\n", "02-03-2025\n", "09-03-2025\n", "16-03-2025\n", "23-03-2025\n", "30-03-2025\n", "06-04-2025\n", "13-04-2025\n", "20-04-2025\n", "27-04-2025\n", "04-05-2025\n", "11-05-2025\n", "18-05-2025\n", "25-05-2025\n", "01-06-2025\n", "08-06-2025\n", "15-06-2025\n", "22-06-2025\n", "29-06-2025\n", "06-07-2025\n", "13-07-2025\n", "20-07-2025\n", "27-07-2025\n", "03-08-2025\n", "10-08-2025\n", "17-08-2025\n", "24-08-2025\n", "31-08-2025\n", "07-09-2025\n", "14-09-2025\n", "21-09-2025\n", "28-09-2025\n", "05-10-2025\n", "12-10-2025\n", "19-10-2025\n", "26-10-2025\n", "02-11-2025\n", "09-11-2025\n", "16-11-2025\n", "23-11-2025\n", "30-11-2025\n", "07-12-2025\n", "14-12-2025\n", "21-12-2025\n", "28-12-2025\n"]}], "source": ["print(f\"\\nAll Sundays in {year}:\")\n", "for sunday in sundays:\n", "    print(sunday.strftime(\"%d-%m-%Y\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}