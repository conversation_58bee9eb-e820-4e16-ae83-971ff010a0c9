{"cells": [{"cell_type": "markdown", "id": "60199db8-116b-4a2c-88fd-0c3769d82ec2", "metadata": {}, "source": ["# Task: Extract Date from Excel File using Pandas\n", "\n", "## Problem Statement:\n", "Given an Excel file containing strings with embedded date information, extract the date portion from those strings and store it in a new column of the DataFrame using Python (without changing the original content).\n", "\n", "## Steps:\n", "1. Import necessary modules: `pandas` and `re`.\n", "2. Read the Excel file using `pd.read_excel()`.\n", "3. Create a new column in the DataFrame to hold extracted dates.\n", "4. Define a regular expression pattern that matches common date formats (e.g., `dd-mm-yyyy`, `yyyy-mm-dd`).\n", "5. Apply the regex pattern to extract the date using `str.extract()` or `re.search()` inside a lambda function.\n", "6. Store the result in the new column of the DataFrame.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a447d0c7-d60c-4133-9071-1206c8ce7bc1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re"]}, {"cell_type": "code", "execution_count": 2, "id": "d7369309-b320-47e8-9d0b-61225844c71a", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(\"date_sample_data.xlsx\");"]}, {"cell_type": "code", "execution_count": 3, "id": "6609a703-bbf6-47fa-bf94-3efa7130f929", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-04-03</td>\n", "      <td>your payment made in 03/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-04-04</td>\n", "      <td>Client meeting on 22/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-04-05</td>\n", "      <td>your payment made in 13/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-04-06</td>\n", "      <td>Party will take place on 24/05/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-04-07</td>\n", "      <td>your payment made in 21/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-04-08</td>\n", "      <td>Client meeting on 02/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2020-04-09</td>\n", "      <td>Bill last date on 31/04/2020 do before a day</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Date                                   Description\n", "0 2020-04-03               your payment made in 03/04/2020\n", "1 2020-04-04                  Client meeting on 22/04/2020\n", "2 2020-04-05               your payment made in 13/04/2020\n", "3 2020-04-06           Party will take place on 24/05/2020\n", "4 2020-04-07               your payment made in 21/04/2020\n", "5 2020-04-08                  Client meeting on 02/04/2020\n", "6 2020-04-09  Bill last date on 31/04/2020 do before a day"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "id": "145cc907-deaa-498b-9685-ffca076b66ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Description</th>\n", "      <th>new_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-04-03</td>\n", "      <td>your payment made in 03/04/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-04-04</td>\n", "      <td>Client meeting on 22/04/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-04-05</td>\n", "      <td>your payment made in 13/04/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-04-06</td>\n", "      <td>Party will take place on 24/05/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-04-07</td>\n", "      <td>your payment made in 21/04/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-04-08</td>\n", "      <td>Client meeting on 02/04/2020</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2020-04-09</td>\n", "      <td>Bill last date on 31/04/2020 do before a day</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Date                                   Description new_Date\n", "0 2020-04-03               your payment made in 03/04/2020     None\n", "1 2020-04-04                  Client meeting on 22/04/2020     None\n", "2 2020-04-05               your payment made in 13/04/2020     None\n", "3 2020-04-06           Party will take place on 24/05/2020     None\n", "4 2020-04-07               your payment made in 21/04/2020     None\n", "5 2020-04-08                  Client meeting on 02/04/2020     None\n", "6 2020-04-09  Bill last date on 31/04/2020 do before a day     None"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df['new_Date']= None\n", "df"]}, {"cell_type": "code", "execution_count": 5, "id": "daf8a8d8-6177-4c0c-b9c9-19193e52500e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 2\n"]}], "source": ["index_set = df.columns.get_loc('Description')\n", "index_date = df.columns.get_loc('new_Date')\n", "\n", "print(index_set, index_date)"]}, {"cell_type": "code", "execution_count": 6, "id": "3293308d-6a5f-48ce-9fa1-9061c14aef1b", "metadata": {}, "outputs": [], "source": ["date_pattern = r'([0-9]{2}\\/[0-9]{2}\\/[0-9]{4})'"]}, {"cell_type": "code", "execution_count": 7, "id": "5f3677d7-6f80-403d-afb6-b59345550894", "metadata": {}, "outputs": [], "source": ["for row in range(0, len(df)):\n", "    Date = re.search(date_pattern,df.iat[row,index_set]).group()\n", "    df.iat[row, index_date] = Date"]}, {"cell_type": "code", "execution_count": 8, "id": "ddcb0e96-d4fc-4381-851b-5379670be782", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Description</th>\n", "      <th>new_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-04-03</td>\n", "      <td>your payment made in 03/04/2020</td>\n", "      <td>03/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-04-04</td>\n", "      <td>Client meeting on 22/04/2020</td>\n", "      <td>22/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-04-05</td>\n", "      <td>your payment made in 13/04/2020</td>\n", "      <td>13/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-04-06</td>\n", "      <td>Party will take place on 24/05/2020</td>\n", "      <td>24/05/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-04-07</td>\n", "      <td>your payment made in 21/04/2020</td>\n", "      <td>21/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-04-08</td>\n", "      <td>Client meeting on 02/04/2020</td>\n", "      <td>02/04/2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2020-04-09</td>\n", "      <td>Bill last date on 31/04/2020 do before a day</td>\n", "      <td>31/04/2020</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Date                                   Description    new_Date\n", "0 2020-04-03               your payment made in 03/04/2020  03/04/2020\n", "1 2020-04-04                  Client meeting on 22/04/2020  22/04/2020\n", "2 2020-04-05               your payment made in 13/04/2020  13/04/2020\n", "3 2020-04-06           Party will take place on 24/05/2020  24/05/2020\n", "4 2020-04-07               your payment made in 21/04/2020  21/04/2020\n", "5 2020-04-08                  Client meeting on 02/04/2020  02/04/2020\n", "6 2020-04-09  Bill last date on 31/04/2020 do before a day  31/04/2020"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}