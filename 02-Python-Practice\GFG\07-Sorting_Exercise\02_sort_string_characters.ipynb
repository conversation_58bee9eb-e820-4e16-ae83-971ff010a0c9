{"cells": [{"cell_type": "markdown", "id": "76c81a1e", "metadata": {}, "source": ["# Task: Sort a String\n", "\n", "## Problem Statement:\n", "Given a string containing only lowercase letters, write a Python program to sort all its characters in **ascending (alphabetical)** order.\n", "\n", "## Steps:\n", "1. Take the input string.\n", "2. Use Python’s built-in `sorted()` function to sort the characters.\n", "3. Join the sorted characters using `''.join()` to form the final string.\n", "4. Return or print the sorted string.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fa0216ce", "metadata": {}, "outputs": [], "source": ["def sort_string(s):\n", "    return \"\".join(sorted(s))"]}, {"cell_type": "code", "execution_count": 2, "id": "04b62983", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["abcde\n"]}], "source": ["S = \"edcab\"\n", "print(sort_string(S))"]}, {"cell_type": "code", "execution_count": 3, "id": "ac32babf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["xyz\n"]}], "source": ["S = \"xzy\"\n", "print(sort_string(S))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}