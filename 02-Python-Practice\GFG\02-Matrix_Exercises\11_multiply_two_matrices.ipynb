{"cells": [{"cell_type": "markdown", "id": "bb3faf9b-fc80-47e4-8a00-f42ea602f1df", "metadata": {}, "source": ["# Task: Multiply Two Matrices\n", "\n", "## Problem Statement:\n", "Given two matrices, write a Python program to multiply them using standard matrix multiplication rules.\n", "\n", "### Steps:\n", "1. Verify that the number of columns in the first matrix equals the number of rows in the second matrix.\n", "2. Initialize a result matrix with appropriate dimensions.\n", "3. Use nested loops to compute the dot product of corresponding rows and columns.\n", "4. Store the computed value in the result matrix.\n", "5. Return the final multiplied matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "cc91fde5-582c-405c-8917-c9aa9bf4c349", "metadata": {}, "outputs": [], "source": ["def multiply(mat1,mat2):\n", "    if len(mat1[0]) != len(mat2):\n", "        raise ValueError(\"Numbers of columns in matrix1 must be equal to the Number of rows in matrix2.\")\n", "\n", "    result = [[0 for _ in range(len(mat2[0]))] for _ in range(len(mat1))]\n", "\n", "    for i in range(len(mat1)):\n", "        for j in range(len(mat2[0])):\n", "            for k in range(len(mat2)):\n", "                result[i][j] += mat1[i][k] * mat2[k][j]\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 2, "id": "ba4d8f78-9343-47dd-a634-a116ade3259f", "metadata": {}, "outputs": [], "source": ["matrix_a = [[1, 2], [3, 4]]\n", "matrix_b = [[5, 6], [7, 8]]"]}, {"cell_type": "code", "execution_count": 3, "id": "a69d281d-dee6-4a5a-a223-0c2ee0cf3d6b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[19, 22], [43, 50]]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply(matrix_a,matrix_b)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}