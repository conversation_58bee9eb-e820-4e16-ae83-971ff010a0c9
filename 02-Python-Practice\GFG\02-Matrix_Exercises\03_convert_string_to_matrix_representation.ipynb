{"cells": [{"cell_type": "markdown", "id": "b1700f21", "metadata": {}, "source": ["# Task: Convert String Matrix Representation to Matrix\n", "\n", "## Problem Statement:\n", "Given a string containing a matrix representation, the task is to write a Python program that converts it into an actual matrix (list of lists) using `split()` and regular expressions.\n", "\n", "### Steps:\n", "1. Use a regular expression to extract all numeric elements from the string and form a plain list.\n", "2. Use `split()` to determine the boundaries for inner dimensions (rows) of the matrix.\n", "3. Group the elements accordingly to construct and return the final 2D matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "efd6f548", "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 2, "id": "5a40c815", "metadata": {}, "outputs": [], "source": ["test_str = \"[gfg,is],[best,for],[all,geeks]\""]}, {"cell_type": "code", "execution_count": 3, "id": "e62c0f5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The original string is : [gfg,is],[best,for],[all,geeks]\n"]}], "source": ["print(\"The original string is : \" + str(test_str))"]}, {"cell_type": "code", "execution_count": 4, "id": "1b74f979", "metadata": {}, "outputs": [], "source": ["flat_1 = re.findall(r\"\\[(.+?)\\]\", str(test_str))"]}, {"cell_type": "code", "execution_count": 5, "id": "6b0b2818", "metadata": {}, "outputs": [], "source": ["res = [sub.split(\",\") for sub in flat_1]"]}, {"cell_type": "code", "execution_count": 7, "id": "1c250696", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Converted Matrix : [['gfg', 'is'], ['best', 'for'], ['all', 'geeks']]\n"]}], "source": ["print(\"Converted Matrix : \" + str(res))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}