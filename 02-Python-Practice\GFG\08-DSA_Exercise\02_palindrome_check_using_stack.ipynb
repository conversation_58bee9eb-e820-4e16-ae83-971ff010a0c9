{"cells": [{"cell_type": "markdown", "id": "60ddd19a", "metadata": {}, "source": ["# Task: Check whether the given string is Palindrome using Stack\n", "\n", "## Problem Statement:\n", "Given a string `s`, determine whether it is a **palindrome** using a **stack**. A palindrome is a string that remains the same when read forward and backward.\n", "\n", "## Steps:\n", "1. Create an empty stack.\n", "2. Traverse the input string and **push each character** onto the stack.\n", "3. Traverse the string again, and for each character, **pop from the stack** and compare.\n", "4. If all characters match during this comparison, then the string is a palindrome; otherwise, it is not.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "18987f0f", "metadata": {}, "outputs": [], "source": ["class Stack:\n", "    def __init__(self):\n", "        self.items = []\n", "\n", "    def push(self, value):\n", "        self.items.append(value)\n", "\n", "    def pop(self):\n", "        if not self.empty():\n", "            return self.items.pop()\n", "        return None\n", "\n", "    def empty(self):\n", "        return len(self.items) == 0"]}, {"cell_type": "code", "execution_count": 2, "id": "65635176", "metadata": {}, "outputs": [], "source": ["def isPalindrome(s):\n", "    stk = Stack()\n", "    for ch in s:\n", "        stk.push(ch)\n", "    for ch in s:\n", "        if ch != stk.pop():\n", "            return False\n", "    return True"]}, {"cell_type": "code", "execution_count": 3, "id": "0b0faa8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}], "source": ["s = input(\"Enter a string: \")\n", "print(s)"]}, {"cell_type": "code", "execution_count": 4, "id": "96066e74", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The string is not a palindrome\n"]}], "source": ["if isPalindrome(s):\n", "    print(\"The string is a palindrome\")\n", "else:\n", "    print(\"The string is not a palindrome\")"]}, {"cell_type": "code", "execution_count": 5, "id": "9c9cc951", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RADAR\n"]}], "source": ["s = input(\"Enter a string: \")\n", "print(s)"]}, {"cell_type": "code", "execution_count": 6, "id": "48b0d8d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The string is a palindrome\n"]}], "source": ["if isPalindrome(s):\n", "    print(\"The string is a palindrome\")\n", "else:\n", "    print(\"The string is not a palindrome\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}