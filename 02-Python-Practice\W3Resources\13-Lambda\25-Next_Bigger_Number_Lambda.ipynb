{"cells": [{"cell_type": "markdown", "id": "7176ef00", "metadata": {}, "source": ["# Task: Next Bigger Number Lambda\n", "\n", "## Problem Statement:\n", "Write a Python program using a **lambda function** to find the **next bigger number** that can be formed by rearranging the digits of a given number. If no such number exists, return an appropriate message.\n", "\n", "### Example:\n", "- Original number: `12` → Next bigger number: `21`\n", "- Original number: `21` → No next bigger number\n", "\n", "## Steps:\n", "1. **Convert the number to a list of digits.**\n", "2. **Generate all permutations** of the digits using `itertools.permutations`.\n", "3. **Filter permutations** that are greater than the original number.\n", "4. **Use a lambda function** to find the smallest permutation greater than the original number.\n", "5. **Return or print** the next bigger number or a message if not found.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a90682ad", "metadata": {}, "outputs": [], "source": ["from itertools import permutations"]}, {"cell_type": "code", "execution_count": 2, "id": "a05821a2", "metadata": {}, "outputs": [], "source": ["def next_bigger_number(n):\n", "    digits = list(str(n))\n", "    perms = set(int(''.join(p)) for p in permutations(digits))\n", "\n", "    bigger = list(filter(lambda x: x > n, perms))\n", "\n", "    return min(bigger) if bigger else False"]}, {"cell_type": "code", "execution_count": 3, "id": "6f3b3d8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original number: 12 → Next bigger number: 21\n", "Original number: 21 → Next bigger number: False\n", "Original number: 201 → Next bigger number: 210\n", "Original number: 445 → Next bigger number: 454\n", "Original number: 102 → Next bigger number: 120\n"]}], "source": ["print(\"Original number: 12 → Next bigger number:\", next_bigger_number(12))\n", "print(\"Original number: 21 → Next bigger number:\", next_bigger_number(21))\n", "print(\"Original number: 201 → Next bigger number:\", next_bigger_number(201))\n", "print(\"Original number: 445 → Next bigger number:\", next_bigger_number(445))\n", "print(\"Original number: 102 → Next bigger number:\", next_bigger_number(102)) "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}