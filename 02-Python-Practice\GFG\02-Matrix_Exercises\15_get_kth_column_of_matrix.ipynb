{"cells": [{"cell_type": "markdown", "id": "108b1cf4-71be-4d2d-9ee8-06ecb6b60fcd", "metadata": {}, "source": ["# Task: Get Kth Column of Matrix\n", "\n", "## Problem Statement:\n", "Given a matrix (2D list), extract the Kth column from it using Python.\n", "\n", "### Steps:\n", "1. Use list comprehension to iterate through each row in the matrix.\n", "2. For each row, select the element at the Kth index.\n", "3. Collect these elements into a new list representing the Kth column.\n", "4. Return the resulting list.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d5b1e3fd-d20d-4a8c-b054-49838b132ab9", "metadata": {}, "outputs": [], "source": ["def get_kth_column(matrix, k):\n", "    return [sub[k] for sub in matrix]"]}, {"cell_type": "code", "execution_count": 2, "id": "277b9a14-062b-4a16-8eb0-c741f777df19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The original matrix is: [[4, 5, 6], [8, 1, 10], [7, 12, 5]]\n"]}], "source": ["test_list = [[4, 5, 6], [8, 1, 10], [7, 12, 5]]\n", "print(\"The original matrix is:\", test_list)"]}, {"cell_type": "code", "execution_count": 3, "id": "bc6a71f8-f8a9-4c73-8982-11bada9d971d", "metadata": {}, "outputs": [], "source": ["k = 2"]}, {"cell_type": "code", "execution_count": 6, "id": "8d7dc070-3ee3-4a08-9df4-c2286046de64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The 2th column of the matrix is: [6, 10, 5]\n"]}], "source": ["result = get_kth_column(test_list, k)\n", "\n", "print(f\"The {k}th column of the matrix is:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}