{"cells": [{"cell_type": "markdown", "id": "d76ad5cb", "metadata": {}, "source": ["# Task: Command-Line File Synchronizer\n", "\n", "## Problem Statement:\n", "Write a Python program to create a **command-line tool** that **synchronizes files between two directories**. The tool should ensure that the destination directory mirrors the source, copying new or updated files and optionally deleting files not present in the source.\n", "\n", "## Steps:\n", "1. **Parse command-line arguments** to get source and destination directories.\n", "2. **Walk through the source directory** using `os.walk()` to list all files and subdirectories.\n", "3. **Compare files** in source and destination using timestamps or hashes to detect changes.\n", "4. **Copy missing or modified files** from source to destination using `shutil.copy2()`.\n", "5. (Optional) **Remove extra files** in the destination that are not present in the source.\n", "6. Print a summary or log of the synchronization actions.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "06034d1b", "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import argparse"]}, {"cell_type": "code", "execution_count": 2, "id": "7e9c3ffa", "metadata": {}, "outputs": [], "source": ["def synchronize(source_dir, destination_dir, delete=False):\n", "    if not os.path.exists(source_dir):\n", "        print(f\"SOurce directory '{source_dir}' does not exist.\")\n", "        return\n", "\n", "    os.makedirs(destination_dir, exist_ok=True)\n", "    actions = []\n", "\n", "    for root, _, files in os.walk(source_dir):\n", "        relative_path = os.path.relpath(root, source_dir)\n", "        dest_path = os.path.join(destination_dir, relative_path)\n", "        os.makedirs(dest_path, exist_ok=True)\n", "\n", "        for file in files:\n", "            source_file = os.path.join(root,file)\n", "            dest_file = os.path.join(dest_path, file)\n", "\n", "\n", "            if not os.path.exists(dest_file) or os.path.getmtime(source_file) > os.path.getmtime(dest_file):\n", "                shutil.copy2(source_file, dest_file)\n", "                actions.append(f\"Copied: {source_file} -> {dest_file}\")\n", "    \n", "    if delete:\n", "        for root, _, files in os.walk(destination_dir):\n", "            relative_path = os.path.relpath(root, destination_dir)\n", "            source_path = os.path.join(source_dir, relative_path)\n", "\n", "            for file in files:\n", "                dest_file = os.path.join(root, file)\n", "                source_file = os.path.join(source_path, file)\n", "                if not os.path.exists(source_file):\n", "                    os.remove(dest_file)\n", "                    actions.append(f\"Deleted: {dest_file}\")\n", "\n", "    print(\"\\nSynchronization Summary:\")\n", "    if actions:\n", "        for action in actions:\n", "            print(\" -\", action)\n", "    else:\n", "        print(\"No changes needed. Directories are already in sync.\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9f8a1b5f", "metadata": {}, "outputs": [], "source": ["# if __name__ == \"__main__\":\n", "#     parser = argparse.ArgumentParser(description=\"Synchronize files between two directories.\")\n", "#     parser.add_argument(\"source\", help=\"Source directory\")\n", "#     parser.add_argument(\"destination\", help=\"Destination directory\")\n", "#     parser.add_argument(\"--delete\", action=\"store_true\", help=\"Delete files not found in source\")\n", "#     args = parser.parse_args()\n", "\n", "#     synchronize(args.source, args.destination, args.delete)\n", "\n", "# # For Command-Line Use only, It'll not work in Jupyter Notebook"]}, {"cell_type": "code", "execution_count": 4, "id": "de4e2d70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Synchronization Summary:\n", " - Copied: source\\CSV.csv -> destination\\.\\CSV.csv\n", " - Copied: source\\CSV_inner.csv -> destination\\.\\CSV_inner.csv\n"]}], "source": ["# Example: run in notebook or script, not via command-line\n", "source = \"source\"\n", "destination = \"destination\"\n", "synchronize(source, destination, delete=True)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}