{"cells": [{"cell_type": "markdown", "id": "7954f5f5", "metadata": {}, "source": ["# Task: Find a Pair with the Highest Product from an Array\n", "\n", "## Problem Statement:\n", "Write a Python program that takes an array of integers and finds a pair of numbers whose product is the highest among all possible pairs in the array. Return that pair and the product value.\n", "\n", "## Steps:\n", "1. Accept or define the input array of integers.\n", "2. Sort the array in ascending order.\n", "3. Calculate the product of the two largest numbers.\n", "4. Calculate the product of the two smallest numbers (in case both are negative and yield a high positive product).\n", "5. Compare the two products and return the corresponding pair along with the product.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e12866d0", "metadata": {}, "outputs": [], "source": ["def max_product_pair(arr):\n", "    if len(arr) < 2:\n", "        return  None, None, None\n", "    \n", "    arr.sort()\n", "\n", "    product1 = arr[-1] * arr[-2]\n", "    product2 = arr[0] * arr[1]\n", "\n", "    if product1 > product2:\n", "        return arr[-1], arr[-2], product1\n", "    else:\n", "        return arr[0], arr[1], product2"]}, {"cell_type": "code", "execution_count": 2, "id": "ceedbda3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8, 7, 56)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["max_product_pair([1, 2, 3, 4, 7, 0, 8, 4])"]}, {"cell_type": "code", "execution_count": 3, "id": "f96e2e88", "metadata": {}, "outputs": [{"data": {"text/plain": ["(-6, -4, 24)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["max_product_pair([0, -1, -2, -4, 5, 0, -6])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}