{"cells": [{"cell_type": "markdown", "id": "4489f773", "metadata": {}, "source": ["# Task: Custom JSON Encoder/Decoder\n", "\n", "## Problem Statement:\n", "Write a Python program to **create a custom JSON encoder and decoder** that can serialize and deserialize **complex Python objects** (such as custom classes). The standard `json` module does not support complex types by default, so we need to extend it using custom logic.\n", "\n", "## Steps:\n", "1. Define a **custom Python class** (e.g., `Person` with attributes like name and age).\n", "2. Create a **custom JSON encoder** by subclassing `json.JSONEncoder` and overriding the `default()` method.\n", "3. Create a **decoder function** that converts a dictionary back into an object using `object_hook` in `json.loads()`.\n", "4. **Encode** a class instance into a JSON string using the custom encoder.\n", "5. **Decode** the JSON string back into a class instance using the custom decoder.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f760c4dc", "metadata": {}, "outputs": [], "source": ["class Person:\n", "    def __init__(self,name,age):\n", "        self.name = name\n", "        self.age = age\n", "\n", "    def __repr__(self):\n", "        return f\"Person(name={self.name!r}, age={self.age!r})\""]}, {"cell_type": "code", "execution_count": 2, "id": "edbc3ce2", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "class CustomEncoder(json.J<PERSON>NEncoder):\n", "    def default(self, obj):\n", "        if isinstance(obj, Person):\n", "            return{\n", "                \"__type__\" : \"Person\",\n", "                \"name\" : obj.name,\n", "                \"age\" : obj.age\n", "            }\n", "        return super().default(obj)"]}, {"cell_type": "code", "execution_count": 3, "id": "69eb88c2", "metadata": {}, "outputs": [], "source": ["def custom_decoder(obj):\n", "    if \"__type__\" in obj and obj[\"__type__\"] == \"Person\":\n", "        return Person(obj[\"name\"], obj[\"age\"])\n", "    return obj"]}, {"cell_type": "code", "execution_count": 4, "id": "9e5861a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Serialized: {\"__type__\": \"Person\", \"name\": \"<PERSON>\", \"age\": 30}\n"]}], "source": ["person = Person(\"<PERSON>\", 30)\n", "json_str = json.dumps(person, cls=CustomEncoder)\n", "print(\"Serialized:\", json_str)"]}, {"cell_type": "code", "execution_count": 5, "id": "997eac87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deserialized: Person(name='<PERSON>', age=30)\n"]}], "source": ["restored_person = json.loads(json_str, object_hook=custom_decoder)\n", "print(\"Deserialized:\", restored_person)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}