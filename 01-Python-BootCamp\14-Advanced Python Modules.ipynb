{"cells": [{"cell_type": "markdown", "id": "7bc8a11b", "metadata": {}, "source": ["# Module 14: Advanced Python Modules"]}, {"cell_type": "markdown", "id": "dd1f5aaf", "metadata": {}, "source": ["## Collections Module"]}, {"cell_type": "markdown", "id": "13a36c04", "metadata": {}, "source": ["**`collections` Module in Python**\n", "\n", "The `collections` module provides alternatives to built-in types that are optimized for specific tasks, such as handling unordered data, counting elements, or working with ordered collections.\n", "\n", "- **`Counter`**: A subclass of `dict` that counts the occurrences of elements in an iterable.\n", "- **`defaultdict`**: A subclass of `dict` that returns a default value if the key is not found.\n", "- **`namedtuple`**: Factory function for creating tuple subclasses with named fields.\n", "- **`OrderedDict`**: A dictionary that remembers the order in which items were inserted.\n", "- **`deque`**: A list-like container that supports fast appends and pops from both ends.\n", "\n", "These data structures help simplify common programming tasks and improve performance for specific use cases.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e642ae0e", "metadata": {}, "outputs": [], "source": ["from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "id": "7c1a1d6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({3: 7, 2: 5, 1: 3, 4: 3, 5: 3})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist = [1,1,1,2,2,2,2,2,3,3,3,3,3,3,3,4,4,4,5,5,5]\n", "Counter(mylist)"]}, {"cell_type": "code", "execution_count": 3, "id": "3d9911fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({10: 3, 'a': 2})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist = ['a','a',10,10,10]\n", "Counter(mylist)"]}, {"cell_type": "code", "execution_count": 4, "id": "7b8fa830", "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({'c': 5, 'a': 4, 'b': 3, 'd': 2, 'e': 1, 'f': 1})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Counter('aaaabbbcccccddef')"]}, {"cell_type": "code", "execution_count": 5, "id": "1db79aa1", "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({'word': 2,\n", "         'How': 1,\n", "         'many': 1,\n", "         'times': 1,\n", "         'does': 1,\n", "         'each': 1,\n", "         'show': 1,\n", "         'up': 1,\n", "         'in': 1,\n", "         'this': 1,\n", "         'sentence': 1,\n", "         'with': 1,\n", "         'a': 1})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["sentence = 'How many times does each word show up in this sentence with a word'\n", "\n", "Counter(sentence.split())"]}, {"cell_type": "code", "execution_count": 6, "id": "a10fe668", "metadata": {}, "outputs": [], "source": ["letters = 'aaabbbbccccccdddddedfffffff'"]}, {"cell_type": "code", "execution_count": 7, "id": "27d79bb3", "metadata": {}, "outputs": [], "source": ["c = Counter(letters)"]}, {"cell_type": "code", "execution_count": 8, "id": "3570c798", "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({'f': 7, 'c': 6, 'd': 6, 'b': 4, 'a': 3, 'e': 1})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["c"]}, {"cell_type": "code", "execution_count": 9, "id": "33d628c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('f', 7), ('c', 6), ('d', 6), ('b', 4), ('a', 3), ('e', 1)]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["c.most_common()"]}, {"cell_type": "code", "execution_count": 10, "id": "fd0492e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('f', 7), ('c', 6)]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["c.most_common(2)"]}, {"cell_type": "code", "execution_count": 11, "id": "dda7696a", "metadata": {}, "outputs": [{"data": {"text/plain": ["['a', 'b', 'c', 'd', 'e', 'f']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["list(c)"]}, {"cell_type": "code", "execution_count": 12, "id": "e5bd1e91", "metadata": {}, "outputs": [], "source": ["from collections import defaultdict"]}, {"cell_type": "code", "execution_count": 13, "id": "1808781d", "metadata": {}, "outputs": [], "source": ["d = {'a': 10}"]}, {"cell_type": "code", "execution_count": 14, "id": "a7d987a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a': 10}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["d"]}, {"cell_type": "code", "execution_count": 15, "id": "06b88b88", "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["d['a']"]}, {"cell_type": "code", "execution_count": 16, "id": "c6c13cd2", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'WRONG'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m d[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mWRONG\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'WRONG'"]}], "source": ["d['WRONG']"]}, {"cell_type": "code", "execution_count": 17, "id": "48d1b9fb", "metadata": {}, "outputs": [], "source": ["d = defaultdict(lambda: 0)"]}, {"cell_type": "code", "execution_count": 18, "id": "0b80c987", "metadata": {}, "outputs": [], "source": ["d['correct'] = 100"]}, {"cell_type": "code", "execution_count": 19, "id": "45b234f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["d['correct']"]}, {"cell_type": "code", "execution_count": 20, "id": "8fa0bdba", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["d[\"Wrong\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "94980859", "metadata": {}, "outputs": [], "source": ["mytuple = (10,20,30)"]}, {"cell_type": "code", "execution_count": 22, "id": "d83f691f", "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["mytuple[0]"]}, {"cell_type": "code", "execution_count": 23, "id": "9e96db5b", "metadata": {}, "outputs": [], "source": ["from collections import namedtuple"]}, {"cell_type": "code", "execution_count": 24, "id": "fcaa35f4", "metadata": {}, "outputs": [], "source": ["Dog = namedtuple('Dog',['age','breed','name'])"]}, {"cell_type": "code", "execution_count": 25, "id": "461f026b", "metadata": {}, "outputs": [], "source": ["sammy = Dog(age=5,breed='<PERSON>sky',name='<PERSON>')"]}, {"cell_type": "code", "execution_count": 26, "id": "23e72968", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dog(age=5, breed='<PERSON>sky', name='<PERSON>')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["sammy"]}, {"cell_type": "code", "execution_count": 27, "id": "47c94b37", "metadata": {}, "outputs": [{"data": {"text/plain": ["__main__.<PERSON>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["type(sammy)"]}, {"cell_type": "code", "execution_count": 28, "id": "cc94ff62", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["sammy.age"]}, {"cell_type": "code", "execution_count": 29, "id": "8b2f5af9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Husky'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["sammy.breed"]}, {"cell_type": "code", "execution_count": 30, "id": "6b47c908", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["sammy.name"]}, {"cell_type": "code", "execution_count": 31, "id": "9cf523be", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["sammy[0]"]}, {"cell_type": "markdown", "id": "7da051b9", "metadata": {}, "source": ["## OS Module"]}, {"cell_type": "markdown", "id": "81d6e10b", "metadata": {}, "source": ["**`os` <PERSON><PERSON><PERSON> in Python**\n", "\n", "The `os` module provides a way to interact with the operating system and perform tasks like file manipulation, process management, and environment management.\n", "\n", "- **File and Directory Operations**: Functions like `os.mkdir()`, `os.remove()`, and `os.rename()` to work with files and directories.\n", "- **Environment Variables**: Use `os.getenv()` and `os.environ` to access and modify environment variables.\n", "- **Path Operations**: Functions like `os.path.join()`, `os.path.exists()`, and `os.path.abspath()` help manipulate and query file paths.\n", "- **Process Management**: Use `os.system()` to run system commands and `os.getpid()` to get the current process ID.\n", "\n", "The `os` module is essential for working with the underlying operating system from Python code.\n"]}, {"cell_type": "code", "execution_count": 32, "id": "303a8724", "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\training-crest\\\\Python'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 33, "id": "37afca0a", "metadata": {}, "outputs": [], "source": ["f = open('practice.txt', 'w+')\n", "f.write('This is a test string')\n", "f.close()"]}, {"cell_type": "code", "execution_count": 34, "id": "1c696a94", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 35, "id": "599cc6d7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\training-crest\\\\Python'"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getcwd()"]}, {"cell_type": "code", "execution_count": 36, "id": "83e4d621", "metadata": {}, "outputs": [{"data": {"text/plain": ["['.ipynb_checkpoints',\n", " '1-DataTypes_Basics.ipynb',\n", " '10-Milestone Project 2.ipynb',\n", " '11-Milestone Project 2(<PERSON>).ipynb',\n", " '12-Decorators.ipynb',\n", " '13-Generators.ipynb',\n", " '14-Advanced Python Modules.ipynb',\n", " '2-DataStructures.ipynb',\n", " '3-FileIO.ipynb',\n", " '4-Statementes and Comparison Operators.ipynb',\n", " '5-Methods in Python.ipynb',\n", " '6-Milestone Project.ipynb',\n", " '7-O<PERSON>.ipynb',\n", " '8-Modules and Packages.ipynb',\n", " '9-Errors and Exceptions Handling.ipynb',\n", " 'Assessments',\n", " 'practice.txt',\n", " 'pylint.py']"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["os.listdir()"]}, {"cell_type": "code", "execution_count": 37, "id": "2e9a5654", "metadata": {}, "outputs": [], "source": ["import shutil"]}, {"cell_type": "code", "execution_count": null, "id": "c7cef02e", "metadata": {}, "outputs": [], "source": ["shutil.move('practice.txt','C:\\Users\\<USER>\\')"]}, {"cell_type": "code", "execution_count": 38, "id": "4efa4815", "metadata": {}, "outputs": [], "source": ["import send2trash"]}, {"cell_type": "code", "execution_count": 39, "id": "a8972e6d", "metadata": {}, "outputs": [], "source": ["send2trash.send2trash('practice.txt')"]}, {"cell_type": "code", "execution_count": 40, "id": "7276f50d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Currently looking at c:\\Users\\<USER>\\training-crest\\Python\n", "\n", "\n", "The subfolders are:\n", "\t Subfolder: .ipynb_checkpoints\n", "\t Subfolder: Assessments\n", "\n", "\n", "The files are:\n", "\t File: 1-DataTypes_Basics.ipynb\n", "\t File: 10-Milestone Project 2.ipynb\n", "\t File: 11-Milestone Project 2(<PERSON>).ipynb\n", "\t File: 12-Decorators.ipynb\n", "\t File: 13-Generators.ipynb\n", "\t File: 14-Advanced Python Modules.ipynb\n", "\t File: 2-DataStructures.ipynb\n", "\t File: 3-FileIO.ipynb\n", "\t File: 4-Statementes and Comparison Operators.ipynb\n", "\t File: 5-Methods in Python.ipynb\n", "\t File: 6-Milestone Project.ipynb\n", "\t File: 7-OOP.ipynb\n", "\t File: 8-Modules and Packages.ipynb\n", "\t File: 9-Errors and Exceptions Handling.ipynb\n", "\t File: pylint.py\n", "\n", "\n", "Currently looking at c:\\Users\\<USER>\\training-crest\\Python\\.ipynb_checkpoints\n", "\n", "\n", "The subfolders are:\n", "\n", "\n", "The files are:\n", "\t File: 4-Statementes and Comparison Operators-checkpoint.ipynb\n", "\t File: DataStructures-checkpoint.ipynb\n", "\t File: DataTypes_Basics-checkpoint.ipynb\n", "\t File: FileIO-checkpoint.ipynb\n", "\t File: Untitled-checkpoint.ipynb\n", "\n", "\n", "Currently looking at c:\\Users\\<USER>\\training-crest\\Python\\Assessments\n", "\n", "\n", "The subfolders are:\n", "\n", "\n", "The files are:\n", "\t File: Module-3_Assesment.ipynb\n", "\t File: Module-5_Assesment.ipynb\n", "\t File: Module-5_Guessing Game Challenge.ipynb\n", "\t File: Module-6_Functions-Practice.ipynb\n", "\t File: Module-6_HomeWork.ipynb\n", "\t File: Module-8_Homework.ipynb\n", "\t File: Module-8_OOP Challenge.ipynb\n", "\n", "\n"]}], "source": ["for folder, subf_folders,files in os.walk(os.getcwd()):\n", "    print(f'Currently looking at {folder}')\n", "    print('\\n')\n", "    print('The subfolders are:')\n", "    for sub_fold in subf_folders:\n", "        print(f'\\t Subfolder: {sub_fold}')\n", "    \n", "    print('\\n')\n", "    print('The files are:')\n", "    for f in files:\n", "        print(f'\\t File: {f}')\n", "    print('\\n')"]}, {"cell_type": "markdown", "id": "53a0c0c0", "metadata": {}, "source": ["## Date Time Module"]}, {"cell_type": "markdown", "id": "c2c13618", "metadata": {}, "source": ["**`datetime` <PERSON><PERSON><PERSON> in Python**\n", "\n", "The `datetime` module provides classes for manipulating dates and times in both simple and complex ways.\n", "\n", "- **`datetime.datetime`**: Represents a single point in time, combining both date and time.\n", "- **`datetime.date`**: Represents just the date (year, month, day).\n", "- **`datetime.time`**: Represents just the time (hour, minute, second, microsecond).\n", "- **`datetime.timedelta`**: Represents the difference between two `datetime` objects (duration).\n", "- **`datetime.strftime()`**: Formats `datetime` objects into strings.\n", "- **`datetime.strptime()`**: Parses strings into `datetime` objects.\n", "\n", "The `datetime` module is useful for working with time-based data, performing date arithmetic, and formatting dates for display.\n"]}, {"cell_type": "code", "execution_count": 41, "id": "1f846663", "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": 42, "id": "1480d0c1", "metadata": {}, "outputs": [], "source": ["mytime = datetime.time(2)"]}, {"cell_type": "code", "execution_count": 43, "id": "0f637061", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["mytime.minute"]}, {"cell_type": "code", "execution_count": 44, "id": "bb6007e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["mytime.hour"]}, {"cell_type": "code", "execution_count": 45, "id": "829e73c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["02:00:00\n"]}], "source": ["print(mytime)"]}, {"cell_type": "code", "execution_count": 46, "id": "933c43b0", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["mytime.microsecond"]}, {"cell_type": "code", "execution_count": 47, "id": "90b9a096", "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.time"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["type(mytime)"]}, {"cell_type": "code", "execution_count": 48, "id": "dbf11b8c", "metadata": {}, "outputs": [], "source": ["today = datetime.date.today()"]}, {"cell_type": "code", "execution_count": 49, "id": "c4a9e58a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-10\n"]}], "source": ["print(today)"]}, {"cell_type": "code", "execution_count": 50, "id": "9dd480bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Thu Jul 10 00:00:00 2025'"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["today.ctime()"]}, {"cell_type": "code", "execution_count": 51, "id": "78b98484", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "mydatetime = datetime(2021,10,3,14,20,1)\n"]}, {"cell_type": "code", "execution_count": 52, "id": "7e309a5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2021-10-03 14:20:01\n"]}], "source": ["print(mydatetime)"]}, {"cell_type": "code", "execution_count": 53, "id": "ff3f75e1", "metadata": {}, "outputs": [], "source": ["mydatetime = mydatetime.replace(year=2020)"]}, {"cell_type": "code", "execution_count": 54, "id": "af5f3de8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2020-10-03 14:20:01\n"]}], "source": ["print(mydatetime)"]}, {"cell_type": "code", "execution_count": 55, "id": "2cdadb1e", "metadata": {}, "outputs": [], "source": ["from datetime import date"]}, {"cell_type": "code", "execution_count": 56, "id": "9869db15", "metadata": {}, "outputs": [], "source": ["date1 = date(2021,11,3)\n", "date2 = date(2020,11,3)\n", "\n", "result = date1 - date2"]}, {"cell_type": "code", "execution_count": 57, "id": "5d8a1a98", "metadata": {}, "outputs": [{"data": {"text/plain": ["365"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["result.days"]}, {"cell_type": "code", "execution_count": 58, "id": "d7841e77", "metadata": {}, "outputs": [], "source": ["datetime1 = datetime(2021,11,3,22,0)\n", "datetime2 = datetime(2020,11,3,12,0)"]}, {"cell_type": "code", "execution_count": 59, "id": "edc88856", "metadata": {}, "outputs": [], "source": ["result = datetime1 - datetime2"]}, {"cell_type": "code", "execution_count": 60, "id": "cfc022e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.<PERSON><PERSON><PERSON>(days=365, seconds=36000)"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 61, "id": "34b08655", "metadata": {}, "outputs": [{"data": {"text/plain": ["36000"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["result.seconds"]}, {"cell_type": "code", "execution_count": 62, "id": "f84d7f38", "metadata": {}, "outputs": [{"data": {"text/plain": ["31572000.0"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["result.total_seconds()"]}, {"cell_type": "markdown", "id": "0fdefe4f", "metadata": {}, "source": ["## Math Module"]}, {"cell_type": "markdown", "id": "3fd60653", "metadata": {}, "source": ["**`math` <PERSON><PERSON><PERSON> in Python**\n", "\n", "The `math` module provides mathematical functions and constants that help with basic mathematical operations.\n", "\n", "- **Mathematical Constants**: Constants like `math.pi` (π), `math.e` (<PERSON><PERSON><PERSON>’s number).\n", "- **Basic Operations**: Functions like `math.sqrt()`, `math.pow()`, `math.fsum()`.\n", "- **Trigonometry**: Functions like `math.sin()`, `math.cos()`, `math.tan()`.\n", "- **Logarithms**: Functions like `math.log()`, `math.log10()`, `math.exp()`.\n", "- **Factorial and Combinations**: Functions like `math.factorial()`, `math.comb()`, `math.perm()`.\n", "\n", "The `math` module is optimized for performing precise mathematical calculations and is widely used in scientific computing and data analysis.\n"]}, {"cell_type": "code", "execution_count": 63, "id": "c7a983a1", "metadata": {}, "outputs": [], "source": ["import math"]}, {"cell_type": "code", "execution_count": 64, "id": "c7c1c143", "metadata": {}, "outputs": [], "source": ["value = 4.35"]}, {"cell_type": "code", "execution_count": 65, "id": "5cfbaafd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n"]}], "source": ["print(math.floor(value))"]}, {"cell_type": "code", "execution_count": 66, "id": "75117967", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n"]}], "source": ["print(math.ceil(value))"]}, {"cell_type": "code", "execution_count": 67, "id": "f62b7cd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n"]}], "source": ["print(round(value))"]}, {"cell_type": "code", "execution_count": 68, "id": "085f1527", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.141592653589793\n"]}], "source": ["print(math.pi)"]}, {"cell_type": "code", "execution_count": 69, "id": "643e9f0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.718281828459045\n"]}], "source": ["print(math.e)"]}, {"cell_type": "code", "execution_count": 70, "id": "37a27d6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inf\n"]}], "source": ["print(math.inf)"]}, {"cell_type": "code", "execution_count": 71, "id": "81f9ef75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nan\n"]}], "source": ["print(math.nan)"]}, {"cell_type": "code", "execution_count": 72, "id": "e240d0f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n"]}], "source": ["print(math.log(math.e))"]}, {"cell_type": "code", "execution_count": 73, "id": "ba1cb47a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.0\n"]}], "source": ["print(math.log(100,10))"]}, {"cell_type": "code", "execution_count": 74, "id": "1b2a1c33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.5440211108893698\n"]}], "source": ["print(math.sin(10))"]}, {"cell_type": "code", "execution_count": 75, "id": "c8c7462b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["90.0\n"]}], "source": ["print(math.degrees(math.pi/2))"]}, {"cell_type": "code", "execution_count": 76, "id": "74fcec1a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.141592653589793\n"]}], "source": ["print(math.radians(180))"]}, {"cell_type": "markdown", "id": "3f9cf0da", "metadata": {}, "source": ["## Random Module"]}, {"cell_type": "markdown", "id": "4259877d", "metadata": {}, "source": ["**`random` Module in Python**\n", "\n", "The `random` module provides functions for generating random numbers and performing random operations.\n", "\n", "- **`random.random()`**: Returns a random float between 0 and 1.\n", "- **`random.randint(a, b)`**: Returns a random integer between `a` and `b` (inclusive).\n", "- **`random.choice(sequence)`**: Selects a random element from a non-empty sequence.\n", "- **`random.shuffle(list)`**: Randomly reorders the elements of a list in place.\n", "- **`random.sample(population, k)`**: Returns a list of `k` unique elements chosen from the population.\n", "\n", "The `random` module is commonly used in simulations, games, and testing where random values are needed.\n"]}, {"cell_type": "code", "execution_count": 77, "id": "a899033e", "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 78, "id": "0e0f6bbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["87"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["random.randint(0,100)"]}, {"cell_type": "code", "execution_count": 79, "id": "b178dc82", "metadata": {}, "outputs": [], "source": ["mylist = list(range(0,20))"]}, {"cell_type": "code", "execution_count": 80, "id": "f0164a60", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["random.choice(mylist)"]}, {"cell_type": "code", "execution_count": 81, "id": "5bdcd7bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[5, 11, 4, 9, 1, 2, 18, 8, 8, 3]"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["random.choices(population=mylist,k=10)"]}, {"cell_type": "code", "execution_count": 82, "id": "808562b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[6, 11, 17, 10, 9, 14, 14, 6, 16, 11]"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["random.choices(population=mylist,k=10,weights=[0.5,0.1,0.1,0.1,0.1,0.1,1,1,1,1,1,1,1,1,1,1,1,1,1,1])"]}, {"cell_type": "code", "execution_count": 83, "id": "fd0e6435", "metadata": {}, "outputs": [], "source": ["random.shuffle(mylist)"]}, {"cell_type": "code", "execution_count": 84, "id": "d295896f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[18, 1, 12, 6, 19, 2, 5, 13, 11, 8, 4, 16, 0, 10, 17, 7, 9, 15, 3, 14]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist"]}, {"cell_type": "code", "execution_count": 85, "id": "b7e35af6", "metadata": {}, "outputs": [{"data": {"text/plain": ["14.69896536133717"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["random.uniform(a=0,b=100)"]}, {"cell_type": "code", "execution_count": 86, "id": "c1aae201", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9819420511659149"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["random.gauss(mu=0,sigma=1)"]}, {"cell_type": "markdown", "id": "6c22059f", "metadata": {}, "source": ["## Python Debugger"]}, {"cell_type": "markdown", "id": "59df8922", "metadata": {}, "source": ["**Debugger in Python**\n", "\n", "The Python debugger, `pdb`, allows you to interactively trace and debug your Python programs. It helps to inspect variables, step through code, and diagnose errors during execution.\n", "\n", "- **Start Debugger**: Use `import pdb; pdb.set_trace()` to pause execution at a specific point and enter the debugger.\n", "- **Common Commands**:\n", "  - `n`: Execute the next line of code.\n", "  - `s`: Step into the function call.\n", "  - `c`: Continue execution until the next breakpoint.\n", "  - `p <expression>`: Print the value of an expression.\n", "  - `q`: <PERSON>uit the debugger.\n", "  \n", "The `pdb` module is useful for inspecting runtime behavior and fixing bugs efficiently.\n"]}, {"cell_type": "code", "execution_count": 87, "id": "c21a6b5e", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can only concatenate list (not \"int\") to list", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[87], line 6\u001b[0m\n\u001b[0;32m      3\u001b[0m z \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m3\u001b[39m\n\u001b[0;32m      5\u001b[0m result1 \u001b[38;5;241m=\u001b[39m y \u001b[38;5;241m+\u001b[39m z\n\u001b[1;32m----> 6\u001b[0m result2 \u001b[38;5;241m=\u001b[39m x \u001b[38;5;241m+\u001b[39m y \u001b[38;5;241m+\u001b[39m z\n\u001b[0;32m      7\u001b[0m result3 \u001b[38;5;241m=\u001b[39m x \u001b[38;5;241m+\u001b[39m result1\n\u001b[0;32m      8\u001b[0m result4 \u001b[38;5;241m=\u001b[39m result1 \u001b[38;5;241m+\u001b[39m result2 \u001b[38;5;241m+\u001b[39m result3\n", "\u001b[1;31mTypeError\u001b[0m: can only concatenate list (not \"int\") to list"]}], "source": ["x = [1,2,3]\n", "y = 2\n", "z = 3\n", "\n", "result1 = y + z\n", "result2 = x + y + z\n", "result3 = x + result1\n", "result4 = result1 + result2 + result3"]}, {"cell_type": "code", "execution_count": 88, "id": "a22b0ef3", "metadata": {}, "outputs": [], "source": ["import pdb"]}, {"cell_type": "code", "execution_count": 89, "id": "112ae233", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> \u001b[1;32mc:\\users\\<USER>\\appdata\\local\\temp\\ipykernel_17132\\139234087.py\u001b[0m(6)\u001b[0;36m<module>\u001b[1;34m()\u001b[0m\n", "\n", "[1, 2, 3]\n", "2\n", "*** NameError: name 'result2' is not defined\n"]}], "source": ["x = [1,2,3]\n", "y = 2\n", "z = 3\n", "\n", "result1 = y + z\n", "pdb.set_trace()\n", "result2 = x + y + z\n", "result3 = x + result1\n", "result4 = result1 + result2 + result3"]}, {"cell_type": "markdown", "id": "73617fef", "metadata": {}, "source": ["## Regular Expressions(RegEX)"]}, {"cell_type": "markdown", "id": "b5d403e5", "metadata": {}, "source": ["**Regular Expressions in Python**\n", "\n", "Regular expressions (regex) are patterns used to match, search, and manipulate text. In Python, they are supported by the `re` module.\n", "\n", "- **Pattern Matching**: Use functions like `re.search()` and `re.match()` to find patterns in strings.\n", "- **Finding All Matches**: `re.findall()` returns all non-overlapping matches in a string.\n", "- **Substitution**: `re.sub()` replaces occurrences of a pattern with a replacement string.\n", "- **Compilation**: `re.compile()` compiles a pattern for reuse and better performance.\n", "\n", "Regular expressions are powerful tools for validating input, extracting data, and transforming text.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "22b18570", "metadata": {}, "outputs": [], "source": ["text = \"The agent's phone number is ************. Call soon!\""]}, {"cell_type": "code", "execution_count": 18, "id": "36bcbf44", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["'phone' in text"]}, {"cell_type": "code", "execution_count": 19, "id": "0e11d36d", "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 20, "id": "2534c61a", "metadata": {}, "outputs": [], "source": ["pattern = 'phone'"]}, {"cell_type": "code", "execution_count": 21, "id": "e5bf407d", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(12, 17), match='phone'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["re.search(pattern,text)"]}, {"cell_type": "code", "execution_count": 22, "id": "04704f25", "metadata": {}, "outputs": [], "source": ["pattern = 'NOT IN TEXT'"]}, {"cell_type": "code", "execution_count": 23, "id": "ee2f37c9", "metadata": {}, "outputs": [], "source": ["re.search(pattern, text)"]}, {"cell_type": "code", "execution_count": 24, "id": "bfa1c3eb", "metadata": {}, "outputs": [], "source": ["pattern = 'phone'"]}, {"cell_type": "code", "execution_count": 25, "id": "417f7a1e", "metadata": {}, "outputs": [], "source": ["match = re.search(pattern, text)"]}, {"cell_type": "code", "execution_count": 26, "id": "52915403", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(12, 17), match='phone'>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["match"]}, {"cell_type": "code", "execution_count": 27, "id": "df5d1fbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["(12, 17)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["match.span()"]}, {"cell_type": "code", "execution_count": 28, "id": "646e56a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["match.start()"]}, {"cell_type": "code", "execution_count": 29, "id": "b84eddeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["17"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["match.end()"]}, {"cell_type": "code", "execution_count": 30, "id": "0d0ded90", "metadata": {}, "outputs": [], "source": ["text = 'my phone once, my phone twice'"]}, {"cell_type": "code", "execution_count": 31, "id": "704c3d03", "metadata": {}, "outputs": [], "source": ["match = re.search('phone', text)"]}, {"cell_type": "code", "execution_count": 32, "id": "b51d64ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(3, 8), match='phone'>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["match"]}, {"cell_type": "code", "execution_count": 33, "id": "04fc7f68", "metadata": {}, "outputs": [], "source": ["matches = re.findall('phone',text)"]}, {"cell_type": "code", "execution_count": 34, "id": "02581a79", "metadata": {}, "outputs": [{"data": {"text/plain": ["['phone', 'phone']"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["matches"]}, {"cell_type": "code", "execution_count": 36, "id": "76050f0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["len(matches)"]}, {"cell_type": "code", "execution_count": 38, "id": "2223b69d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["phone\n", "phone\n"]}], "source": ["for match in re.finditer('phone',text):\n", "    print(match.group())"]}, {"cell_type": "markdown", "id": "7d0eca06", "metadata": {}, "source": ["**Character Identifiers in Regular Expressions**\n", "\n", "Character identifiers are special sequences in regex that help define patterns for matching characters:\n", "\n", "- **`.`**: Matches any character except a newline.\n", "- **`\\d`**: Matches any digit (0–9).\n", "- **`\\D`**: Matches any non-digit character.\n", "- **`\\w`**: Matches any alphanumeric character and underscore.\n", "- **`\\W`**: Matches any non-alphanumeric character.\n", "- **`\\s`**: Matches any whitespace character (space, tab, newline).\n", "- **`\\S`**: Matches any non-whitespace character.\n", "- **`^`**: Matches the start of a string.\n", "- **`$`**: Matches the end of a string.\n", "\n", "These identifiers make it easier to build complex search patterns.\n"]}, {"cell_type": "code", "execution_count": 40, "id": "5eea799b", "metadata": {}, "outputs": [], "source": ["text = 'My phone number is ************'"]}, {"cell_type": "code", "execution_count": 41, "id": "1051956a", "metadata": {}, "outputs": [], "source": ["phone = re.search(\"************\", text)"]}, {"cell_type": "code", "execution_count": 42, "id": "02f8355e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(19, 31), match='************'>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["phone"]}, {"cell_type": "code", "execution_count": 43, "id": "bcfad7ed", "metadata": {}, "outputs": [], "source": ["phone = re.search(r'\\d\\d\\d-\\d\\d\\d-\\d\\d\\d\\d',text)"]}, {"cell_type": "code", "execution_count": 44, "id": "e784f4c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(19, 31), match='************'>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["phone"]}, {"cell_type": "code", "execution_count": 45, "id": "9566189f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'************'"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["phone.group()"]}, {"cell_type": "markdown", "id": "036b377b", "metadata": {}, "source": ["**Quantifiers in Regular Expressions**\n", "\n", "Quantifiers define how many times a character or group can repeat in a pattern:\n", "\n", "- **`*`**: Matches 0 or more occurrences.\n", "- **`+`**: Matches 1 or more occurrences.\n", "- **`?`**: Matches 0 or 1 occurrence.\n", "- **`{n}`**: Matches exactly `n` occurrences.\n", "- **`{n,}`**: Matches `n` or more occurrences.\n", "- **`{n,m}`**: Matches between `n` and `m` occurrences.\n", "\n", "Quantifiers help control the amount of text a pattern should match.\n"]}, {"cell_type": "code", "execution_count": 46, "id": "7959ec5c", "metadata": {}, "outputs": [], "source": ["phone = re.search(r'\\d{3}-\\d{3}-\\d{4}',text)"]}, {"cell_type": "code", "execution_count": 47, "id": "fe340a79", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(19, 31), match='************'>"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["phone"]}, {"cell_type": "code", "execution_count": 48, "id": "d63240b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'************'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["phone.group()"]}, {"cell_type": "code", "execution_count": 49, "id": "895f7395", "metadata": {}, "outputs": [], "source": ["phone_pattern = re.compile(r'(\\d{3})-(\\d{3})-(\\d{4})')"]}, {"cell_type": "code", "execution_count": 50, "id": "fb1dd8cf", "metadata": {}, "outputs": [], "source": ["results = re.search(phone_pattern, text)"]}, {"cell_type": "code", "execution_count": 51, "id": "ca6c4794", "metadata": {}, "outputs": [{"data": {"text/plain": ["'************'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["results.group()"]}, {"cell_type": "code", "execution_count": 52, "id": "2cc56ed6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'408'"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["results.group(1)"]}, {"cell_type": "code", "execution_count": 53, "id": "38dc0cd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'555'"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["results.group(2)"]}, {"cell_type": "code", "execution_count": 54, "id": "294c2022", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1234'"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["results.group(3)"]}, {"cell_type": "code", "execution_count": null, "id": "7cc694dc", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "no such group", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[55], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m results\u001b[38;5;241m.\u001b[39mgroup(\u001b[38;5;241m4\u001b[39m)\n", "\u001b[1;31mIndexError\u001b[0m: no such group"]}], "source": ["results.group(4) #This will give an error as 4th group doesn't exist"]}, {"cell_type": "markdown", "id": "01fe6bf9", "metadata": {}, "source": ["### Additional RegEx Syntax"]}, {"cell_type": "code", "execution_count": null, "id": "a4f7fbc2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(4, 7), match='cat'>"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["re.search(r'cat','The cat is here')"]}, {"cell_type": "code", "execution_count": 58, "id": "043e0790", "metadata": {}, "outputs": [], "source": ["re.search(r'cat','The dog is here')"]}, {"cell_type": "code", "execution_count": 59, "id": "b5b1ed1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(4, 7), match='cat'>"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["re.search(r'cat|dog','The cat is here')  ## If you have to search for <PERSON> or <PERSON>."]}, {"cell_type": "code", "execution_count": 60, "id": "207ce9ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["['at', 'at', 'at']"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(r'at','The cat in the hat sat there.')"]}, {"cell_type": "markdown", "id": "63fe0655", "metadata": {}, "source": ["**Wildcard in Regular Expressions**\n", "\n", "The wildcard character in regex is:\n", "\n", "- **`.` (dot)**: Matches **any single character** except a newline (`\\n`).\n", "\n", "It is often used to represent unknown or variable characters in a pattern. To match a literal dot, use `\\.` instead.\n", "\n", "The wildcard is useful when the exact character is not known or can vary.\n"]}, {"cell_type": "code", "execution_count": 61, "id": "65eb4cb3", "metadata": {}, "outputs": [{"data": {"text/plain": ["['cat', 'hat', 'sat']"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(r'.at','The cat in the hat sat there.')"]}, {"cell_type": "code", "execution_count": 64, "id": "96d402f8", "metadata": {}, "outputs": [{"data": {"text/plain": ["['e cat', 'e hat', 'splat']"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(r'...at','The cat in the hat went splat.')"]}, {"cell_type": "markdown", "id": "099a61ba", "metadata": {}, "source": ["- **`^`**: Matches the start of a string."]}, {"cell_type": "code", "execution_count": 65, "id": "e934764a", "metadata": {}, "outputs": [{"data": {"text/plain": ["['1']"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(r'^\\d','1 is a number')"]}, {"cell_type": "markdown", "id": "20d03330", "metadata": {}, "source": ["- **`$`**: Matches the end of a string."]}, {"cell_type": "code", "execution_count": 66, "id": "ad6a3c05", "metadata": {}, "outputs": [{"data": {"text/plain": ["['2']"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(r'\\d$','The number is 2')"]}, {"cell_type": "code", "execution_count": 67, "id": "83704240", "metadata": {}, "outputs": [], "source": ["phrase = 'there are 3 numbers 34 inside 5 this sentence'"]}, {"cell_type": "code", "execution_count": 72, "id": "7d88bf57", "metadata": {}, "outputs": [], "source": ["pattern = r'[^\\d]+'"]}, {"cell_type": "code", "execution_count": 73, "id": "fb3e2491", "metadata": {}, "outputs": [{"data": {"text/plain": ["['there are ', ' numbers ', ' inside ', ' this sentence']"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(pattern,phrase)"]}, {"cell_type": "code", "execution_count": 74, "id": "8fc47efa", "metadata": {}, "outputs": [], "source": ["test_phrase = 'This is a string! But it has punctuation. How can we remove it?'"]}, {"cell_type": "code", "execution_count": 76, "id": "f7b95d35", "metadata": {}, "outputs": [], "source": ["clean = re.findall(r'[^!.? ]+',test_phrase)"]}, {"cell_type": "code", "execution_count": 77, "id": "6ac97443", "metadata": {}, "outputs": [{"data": {"text/plain": ["['This',\n", " 'is',\n", " 'a',\n", " 'string',\n", " 'But',\n", " 'it',\n", " 'has',\n", " 'punctuation',\n", " 'How',\n", " 'can',\n", " 'we',\n", " 'remove',\n", " 'it']"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["clean"]}, {"cell_type": "code", "execution_count": 78, "id": "a42133e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'This is a string But it has punctuation How can we remove it'"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["' '.join(clean)"]}, {"cell_type": "code", "execution_count": 79, "id": "002073ea", "metadata": {}, "outputs": [], "source": ["text = 'Only find the hypen-words in this sentence. But you do not know hoe long-ish they are'"]}, {"cell_type": "code", "execution_count": 84, "id": "9578f0d6", "metadata": {}, "outputs": [], "source": ["pattern = r'\\w+-\\w+'"]}, {"cell_type": "code", "execution_count": 85, "id": "191da26f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['hypen-words', 'long-ish']"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["re.findall(pattern,text)"]}, {"cell_type": "code", "execution_count": 86, "id": "20d00640", "metadata": {}, "outputs": [], "source": ["text = 'Hello, would you like some catfish?'\n", "texttwo = 'Hello, would you like to take a catnap?'\n", "textthree = 'Hello, have you seen this caterpiller?'"]}, {"cell_type": "code", "execution_count": 87, "id": "2f58b27b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(27, 34), match='catfish'>"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["re.search(r'cat(fish|nap|claw)',text)"]}, {"cell_type": "code", "execution_count": 88, "id": "a76044a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(32, 38), match='catnap'>"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["re.search(r'cat(fish|nap|claw)',texttwo)"]}, {"cell_type": "code", "execution_count": 90, "id": "4075c713", "metadata": {}, "outputs": [], "source": ["re.search(r'cat(fish|nap|claw)',textthree) "]}, {"cell_type": "markdown", "id": "3da08985", "metadata": {}, "source": ["## Timing your code"]}, {"cell_type": "markdown", "id": "2ac13771", "metadata": {}, "source": ["**Timing Your Code in Python**\n", "\n", "Timing code helps measure how long a block of code takes to run. Python provides several ways to do this:\n", "\n", "- **`time` module**: Use `time.time()` to get the current time in seconds.\n", "- **`timeit` module**: Provides a more precise way to measure small code snippets by running them multiple times.\n", "- **`perf_counter()`**: From the `time` module, gives the highest available resolution timer.\n", "\n", "These tools are useful for benchmarking and optimizing performance.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3f376fc0", "metadata": {}, "outputs": [], "source": ["def func_one(n):\n", "    return [str(num) for num in range(n)]"]}, {"cell_type": "code", "execution_count": 2, "id": "5f81fe89", "metadata": {}, "outputs": [{"data": {"text/plain": ["['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["func_one(10)"]}, {"cell_type": "code", "execution_count": 3, "id": "4b3078b5", "metadata": {}, "outputs": [], "source": ["def func_two(n):\n", "    return list(map(str,range(n)))"]}, {"cell_type": "code", "execution_count": 4, "id": "23455913", "metadata": {}, "outputs": [{"data": {"text/plain": ["['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["func_two(10)"]}, {"cell_type": "code", "execution_count": 5, "id": "95109d9c", "metadata": {}, "outputs": [], "source": ["import time"]}, {"cell_type": "code", "execution_count": 9, "id": "77fe3d2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.30882692337036133\n"]}], "source": ["#Current time before\n", "start_time = time.time()\n", "\n", "#RUN CODE\n", "result = func_one(1000000)\n", "\n", "#Current time after runing Code\n", "end_time = time.time()\n", "\n", "#Elapsed time\n", "elapsed_time = end_time - start_time\n", "\n", "print(elapsed_time)"]}, {"cell_type": "code", "execution_count": 10, "id": "286df155", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.25287318229675293\n"]}], "source": ["#Current time before\n", "start_time = time.time()\n", "\n", "#RUN CODE\n", "result = func_two(1000000)\n", "\n", "#Current time after runing Code\n", "end_time = time.time()\n", "\n", "#Elapsed time\n", "elapsed_time = end_time - start_time\n", "\n", "print(elapsed_time)"]}, {"cell_type": "code", "execution_count": 12, "id": "56d498f3", "metadata": {}, "outputs": [], "source": ["import timeit"]}, {"cell_type": "code", "execution_count": 13, "id": "fdf2ba9a", "metadata": {}, "outputs": [], "source": ["stmt = '''func_one(100)'''"]}, {"cell_type": "code", "execution_count": 14, "id": "2c6b3696", "metadata": {}, "outputs": [], "source": ["setup = '''\n", "def func_one(n):\n", "    return [str(num) for num in range(n)]\n", "'''"]}, {"cell_type": "code", "execution_count": 17, "id": "c058ab1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["2.456511500000488"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["timeit.timeit(stmt,setup,number=100000)"]}, {"cell_type": "code", "execution_count": 18, "id": "f6b0e01f", "metadata": {}, "outputs": [], "source": ["stmt = '''\n", "func_two(100)\n", "'''"]}, {"cell_type": "code", "execution_count": 19, "id": "b5d12fec", "metadata": {}, "outputs": [], "source": ["setup = '''\n", "def func_two(n):\n", "    return list(map(str,range(n)))\n", "'''"]}, {"cell_type": "code", "execution_count": 20, "id": "d3d0c7a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.9077610000094865"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["timeit.timeit(stmt,setup,number=100000)"]}, {"cell_type": "code", "execution_count": 21, "id": "4990e2f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["30.5 μs ± 10.7 μs per loop (mean ± std. dev. of 7 runs, 10,000 loops each)\n"]}], "source": ["%%timeit\n", "func_one(100)"]}, {"cell_type": "code", "execution_count": 22, "id": "f60a32ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19.2 μs ± 7.87 μs per loop (mean ± std. dev. of 7 runs, 10,000 loops each)\n"]}], "source": ["%%timeit\n", "func_two(100)"]}, {"cell_type": "markdown", "id": "ca9b2a48", "metadata": {}, "source": ["## Zipping and Unzipping files in Python"]}, {"cell_type": "code", "execution_count": null, "id": "748a8101", "metadata": {}, "outputs": [], "source": ["f = open('fileone.txt','w+')\n", "f.write('One File')\n", "f.close()"]}, {"cell_type": "code", "execution_count": null, "id": "53d46a77", "metadata": {}, "outputs": [], "source": ["f = open('filetwo.txt','w+')\n", "f.write('Two File')\n", "f.close()"]}, {"cell_type": "code", "execution_count": 23, "id": "eb124102", "metadata": {}, "outputs": [], "source": ["import zipfile"]}, {"cell_type": "code", "execution_count": null, "id": "e23670e6", "metadata": {}, "outputs": [], "source": ["comp_file = zipfile.ZipFile('comp_file.zip','w')"]}, {"cell_type": "code", "execution_count": null, "id": "7a47ec72", "metadata": {}, "outputs": [], "source": ["comp_file.write('fileone.txt',compress_type=zipfile.ZIP_DEFLATED)"]}, {"cell_type": "code", "execution_count": null, "id": "1a07ef29", "metadata": {}, "outputs": [], "source": ["comp_file.write('filetwo.txt',compress_type=zipfile.ZIP_DEFLATED)"]}, {"cell_type": "code", "execution_count": null, "id": "e927ab91", "metadata": {}, "outputs": [], "source": ["comp_file.close()"]}, {"cell_type": "code", "execution_count": null, "id": "2c796063", "metadata": {}, "outputs": [], "source": ["zip_obj = zipfile.ZipFile('comp_file.zip','r')"]}, {"cell_type": "code", "execution_count": null, "id": "082ebdd8", "metadata": {}, "outputs": [], "source": ["zip_obj.extractall(\"extracted_content\")"]}, {"cell_type": "code", "execution_count": 24, "id": "a3427440", "metadata": {}, "outputs": [], "source": ["import shutil"]}, {"cell_type": "code", "execution_count": null, "id": "4499e0f8", "metadata": {}, "outputs": [], "source": ["dir_to_zip = 'Demo Path'"]}, {"cell_type": "code", "execution_count": null, "id": "8a2c1d06", "metadata": {}, "outputs": [], "source": ["output_filename = 'example'"]}, {"cell_type": "code", "execution_count": null, "id": "c9ac3195", "metadata": {}, "outputs": [], "source": ["shutil.make_archive(output_filename,'zip',dir_to_zip)"]}, {"cell_type": "code", "execution_count": null, "id": "bc5e5b80", "metadata": {}, "outputs": [], "source": ["shutil.unpack_archive('example.zip','final_unzip','zip')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}