{"cells": [{"cell_type": "markdown", "id": "e47c5139", "metadata": {}, "source": ["# Task: Reverse a Stack using Python\n", "\n", "## Problem Statement:\n", "Given a stack data structure, write a Python program to **reverse** the elements of the stack using recursion **without using any additional data structures** (like another stack or queue).\n", "\n", "## Steps:\n", "1. **Pop** all elements from the stack recursively.\n", "2. After reaching the bottom of the stack, start **inserting elements at the bottom** recursively.\n", "3. Use two helper functions:\n", "   - `reverse_stack(stack)`: To pop all elements recursively and reverse the stack.\n", "   - `insert_at_bottom(stack, item)`: To insert an element at the **bottom** of the stack.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "03c97e51", "metadata": {}, "outputs": [], "source": ["class Stack:\n", "    def __init__(self):\n", "        self.Elements = []\n", "    \n", "    def push(self,value):\n", "        self.Elements.append(value)\n", "\n", "    def pop(self):\n", "        return self.Elements.pop()\n", "    \n", "    def empty(self):\n", "        return self.Elements == []\n", "    \n", "    def show(self):\n", "        for value in reversed(self.Elements):\n", "            print(value)"]}, {"cell_type": "code", "execution_count": 2, "id": "96611a59", "metadata": {}, "outputs": [], "source": ["def BottomInsert(s,value):\n", "    if s.empty():\n", "        s.push(value)\n", "    else:\n", "        popped = s.pop()\n", "        BottomInsert(s,value)\n", "        s.push(popped)"]}, {"cell_type": "code", "execution_count": 3, "id": "4e54835f", "metadata": {}, "outputs": [], "source": ["def Reverse(s):\n", "    if not s.empty():\n", "        popped = s.pop()\n", "        Reverse(s)\n", "        BottomInsert(s,popped)"]}, {"cell_type": "code", "execution_count": 4, "id": "b3b8161a", "metadata": {}, "outputs": [], "source": ["stk = Stack()\n", "stk.push(1)\n", "stk.push(2)\n", "stk.push(3)\n", "stk.push(4)\n", "stk.push(5)"]}, {"cell_type": "code", "execution_count": 5, "id": "fd8f4b83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original Stack\n", "5\n", "4\n", "3\n", "2\n", "1\n"]}], "source": ["print(\"Original Stack\")\n", "stk.show()"]}, {"cell_type": "code", "execution_count": 6, "id": "b86db042", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Stack after <PERSON><PERSON>ing\n", "1\n", "2\n", "3\n", "4\n", "5\n"]}], "source": ["print(\"\\nStack after <PERSON><PERSON><PERSON>\")\n", "Reverse(stk)\n", "stk.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}