{"cells": [{"cell_type": "markdown", "id": "592004ee-63da-41a5-baf4-20fa3496e816", "metadata": {}, "source": ["# Task: Categorize Password as Strong or Weak using Regex in Python\n", "\n", "## Problem Statement:\n", "Given a password string, determine if it is **Strong** or **Weak** based on a series of rules. If weak, provide the reason.\n", "\n", "## Conditions for a Strong Password:\n", "1. Length must be **between 9 and 20 characters**.\n", "2. Cannot contain **newlines** or **spaces**.\n", "3. Should **not have three or more repeating characters** in a row.\n", "4. Should **not have repeating patterns** (minimum length 2).\n", "\n", "## Steps:\n", "\n", "1. **Input the password** as a string.\n", "2. **Check the length** of the password:\n", "   - If it's less than 9 or greater than 20 → Weak: \"Password must be 9-20 characters long.\"\n", "3. **Check for newline or space characters** using `re.search(r'\\s', password)`:\n", "   - If present → Weak: \"Password cannot contain spaces or newline characters.\"\n", "4. **Check for three or more repeating characters** using `re.search(r'(.)\\1{2,}', password)`:\n", "   - If found → Weak: \"Password cannot contain three or more repeating characters in a row.\"\n", "5. **Check for repeated patterns** of at least 2 characters using `re.search(r'(..+?)\\1', password)`:\n", "   - If found → Weak: \"Password contains repeating patterns.\"\n", "6. If all above checks pass → Strong password.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "44e7e695-a085-4a27-86c9-bd7243cd301e", "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 2, "id": "bd4e8a0f-b583-4032-9eba-3817aebf48eb", "metadata": {}, "outputs": [], "source": ["def password(v):\n", "    if v == \"\\n\" or v == \" \": \n", "        return \"Password cannot be a newline or space!\"\n", "\n", "    if 9 <= len(v) <= 20: \n", "        if re.search(r'(.)\\1\\1', v): \n", "            return \"Weak Password: Same character repeats three or more times in a row\"\n", "\n", "        if re.search(r'(..)(.*?)\\1', v): \n", "            return \"Weak password: Same string pattern repetition\"\n", "\n", "        else: \n", "            return \"Strong Password!\"\n", "\n", "    else: \n", "        return \"Password length must be 9-20 characters!\""]}, {"cell_type": "code", "execution_count": 3, "id": "18cf40af-7850-4a85-98f4-af06fb67231b", "metadata": {}, "outputs": [], "source": ["def main(): \n", "    print(password(\"Qggf!@ghf3\")) \n", "    print(password(\"Gggksforgeeks\")) \n", "    print(password(\"aaabnil1gu\")) \n", "    print(password(\"Aasd!feasn\")) \n", "    print(password(\"772*hd897\")) \n", "    print(password(\" \")) "]}, {"cell_type": "code", "execution_count": 4, "id": "e4fe8693-dd58-41cd-adcb-a45638eb298b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Strong Password!\n", "Weak password: Same string pattern repetition\n", "Weak Password: Same character repeats three or more times in a row\n", "Weak password: Same string pattern repetition\n", "Strong Password!\n", "Password cannot be a newline or space!\n"]}], "source": ["if __name__ == '__main__': \n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}