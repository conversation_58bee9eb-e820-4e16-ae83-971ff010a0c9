{"cells": [{"cell_type": "markdown", "id": "d01d4c36", "metadata": {}, "source": ["# Task: Words with Prime Lengths\n", "\n", "## Problem Statement:\n", "Write a Python program that takes a sentence or string as input and returns a new string containing only those words whose lengths are **prime numbers**.\n", "\n", "## Steps:\n", "1. **Define a helper function** to check if a number is prime.\n", "2. **Split the input string** into individual words.\n", "3. **Filter words** based on whether their lengths are prime using the helper function.\n", "4. **Join and return** the filtered words as a single string.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "349d469e", "metadata": {}, "outputs": [], "source": ["import math"]}, {"cell_type": "code", "execution_count": 2, "id": "6404d534", "metadata": {}, "outputs": [], "source": ["def isPrime(n):\n", "    if n <= 1:\n", "        return False\n", "    if n <= 3:\n", "        return True\n", "    if n % 2 == 0 or n % 3 == 0:\n", "        return False\n", "    \n", "    for i in range(5, int(math.isqrt(n)) + 1, 6):\n", "        if n % i == 0 or n % (i + 2) == 0:\n", "            return False\n", "    return True"]}, {"cell_type": "code", "execution_count": 3, "id": "d56bcfd8", "metadata": {}, "outputs": [], "source": ["def words_with_prime_lengths(s):\n", "    ans = []\n", "    for word in s.split():\n", "        if isPrime(len(word)):\n", "            ans.append(word)\n", "    return ans"]}, {"cell_type": "code", "execution_count": 4, "id": "a19a94b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['The', 'quick', 'brown', 'fox', 'jumps', 'the']\n"]}], "source": ["s = 'The quick brown fox jumps over the lazy dog.'\n", "print(words_with_prime_lengths(s))"]}, {"cell_type": "code", "execution_count": 5, "id": "007345b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Omicron', 'Effect:', 'Foreign', 'Flights', \"Won't\", 'On', 'Dec', '15,']\n"]}], "source": ["s = \"Omicron Effect: Foreign Flights Won't Resume On Dec 15, Decision Later.\"\n", "print(words_with_prime_lengths(s))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}