{"cells": [{"cell_type": "markdown", "id": "55432e23", "metadata": {}, "source": ["# Task: Find Missing Number in an Array of Numbers Between 10 and 20\n", "\n", "## Problem Statement:\n", "Write a Python program that takes an array of integers ranging from 10 to 20 (inclusive), with exactly one number missing, and determines the missing number.\n", "\n", "## Steps:\n", "1. Define the complete range of numbers from 10 to 20 using `range()`.\n", "2. Accept or define the input list with one number missing from that range.\n", "3. Convert the input list to a set.\n", "4. Subtract the set of input numbers from the set of the full range.\n", "5. Return or print the single element from the resulting set as the missing number.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "318bf4de", "metadata": {}, "outputs": [], "source": ["def finding_missing_number(arr):\n", "    full_set = set(range(10,21))\n", "    input_set = set(arr)\n", "    missing = full_set - input_set\n", "\n", "    if not missing:\n", "        return \"No Numbers are missing\"\n", "    else:\n", "        return missing"]}, {"cell_type": "code", "execution_count": 2, "id": "5b36db1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{20}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["finding_missing_number([10, 11, 12, 13, 14, 15, 16, 17, 18, 19])"]}, {"cell_type": "code", "execution_count": 3, "id": "06d8fba5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{11, 15}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["finding_missing_number([10, 12, 13, 14, 16, 17, 18, 19, 20])"]}, {"cell_type": "code", "execution_count": 4, "id": "e80078ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["'No Numbers are missing'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["finding_missing_number([10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}