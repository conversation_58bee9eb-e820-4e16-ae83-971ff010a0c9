{"cells": [{"cell_type": "markdown", "id": "05c8e9cd-8141-41ad-8fab-0d2e20477b10", "metadata": {}, "source": ["# Task: Transpose a Matrix in Single Line in Python\n", "\n", "## Problem Statement:\n", "Transposing a matrix means flipping it over its diagonal—converting rows to columns and columns to rows. The task is to perform this operation in a single line of Python code using list comprehension.\n", "\n", "### Steps:\n", "1. Use list comprehension to iterate over column indices of the original matrix.\n", "2. For each column index, collect corresponding elements from each row to form a new row in the transposed matrix.\n", "3. Assign the result to a new matrix and return it as the transpose.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "48059b00-8951-494c-92be-d4cc5ceec85f", "metadata": {}, "outputs": [], "source": ["m = [[1, 2], [3, 4], [5, 6]]"]}, {"cell_type": "code", "execution_count": 2, "id": "24a5ccd3-9780-4473-9e72-88574e9bbf3e", "metadata": {}, "outputs": [], "source": ["res = [[m[j][i] for j in range(len(m))] for i in range(len(m[0]))]"]}, {"cell_type": "code", "execution_count": 3, "id": "9e47dbe6-301e-4732-80ee-c2e85b36e35d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 3, 5]\n", "[2, 4, 6]\n"]}], "source": ["for row in res:\n", "    print(row)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}