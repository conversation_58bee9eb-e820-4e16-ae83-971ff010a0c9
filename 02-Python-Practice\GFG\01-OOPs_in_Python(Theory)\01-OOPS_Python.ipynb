{"cells": [{"cell_type": "markdown", "id": "b13e608d-b2d1-4cef-8ba0-5f8a80e5b597", "metadata": {}, "source": ["# Inner Class"]}, {"cell_type": "code", "execution_count": 9, "id": "b4a809b3-75fc-4e89-b6b2-3d4d99392838", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am an inner class!!!\n", "Jaydeep-90\n", "Hp\n", "I am an inner class!!!\n"]}], "source": ["class student():\n", "    def __init__(self,name,roll_no):\n", "        self.name = name\n", "        self.roll_no = roll_no\n", "        #print(\"i am getting called from inside\")\n", "        self.l1 = self.laptop()\n", "\n", "    def show(self):\n", "        print(self.name + \"-\" + str(self.roll_no))\n", "        print(self.l1.show())\n", "\n", "    class laptop():\n", "        def __init__(self):\n", "            print(\"I am an inner class!!!\")\n", "\n", "        def show(self):\n", "            return f\"Hp\"\n", "\n", "\n", "s1 = student(\"<PERSON><PERSON><PERSON>\",90)\n", "s1.show()\n", "l2 = student.laptop()"]}, {"cell_type": "markdown", "id": "a9a0fc39-cab1-4b50-a5a2-2e90e14eed59", "metadata": {}, "source": ["# Inheritance"]}, {"cell_type": "code", "execution_count": null, "id": "ad41eda9-de1d-4797-a3db-30b84494c665", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B(A):\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")"]}, {"cell_type": "code", "execution_count": 25, "id": "fdbc6ce2-e54b-478a-86d4-128294d38f32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["operation_1\n"]}], "source": ["a1 = A()\n", "a1.operation_1()"]}, {"cell_type": "code", "execution_count": 19, "id": "f72ae999-25ec-4464-842c-e519afc1394d", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'A' object has no attribute 'operation_3'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[19], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m a1\u001b[38;5;241m.\u001b[39moperation_3()\n", "\u001b[1;31mAttributeError\u001b[0m: 'A' object has no attribute 'operation_3'"]}], "source": ["a1.operation_3()"]}, {"cell_type": "code", "execution_count": 20, "id": "8d597aa9-e44a-4073-95c7-4350f62bbec8", "metadata": {}, "outputs": [], "source": ["b1 = B()"]}, {"cell_type": "code", "execution_count": 21, "id": "fc5f718d-d30b-4aa2-a839-820c768e860d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["operation_1\n", "operation_3\n"]}], "source": ["b1.operation_1()\n", "b1.operation_3()"]}, {"cell_type": "markdown", "id": "dd474cc2-ed0c-4b18-bb9b-ab23720099f0", "metadata": {}, "source": ["### Multilevel Inheritance"]}, {"cell_type": "code", "execution_count": 22, "id": "a2a0bc10-4451-4d3f-82ab-d5767b08d039", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B(A):\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")\n", "        \n", "class C(B):\n", "    def operation_5(self):\n", "        print(\"operation_5\")\n", "    def operation_6(self):\n", "        print(\"operation_6\")\n", "        "]}, {"cell_type": "markdown", "id": "8a3908ff-3c59-4555-bde1-c2aa8f0791f4", "metadata": {}, "source": ["### Multiple Inheritance"]}, {"cell_type": "code", "execution_count": 37, "id": "b9b5dd67-41cd-4bed-82ec-ac296aec5ed5", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B():\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")\n", "        \n", "class C(A,B):\n", "    def operation_5(self):\n", "        print(\"operation_5\")\n", "    def operation_6(self):\n", "        print(\"operation_6\")\n", "        "]}, {"cell_type": "markdown", "id": "6dcce852-bc90-4224-a05e-3bae58a5a53c", "metadata": {}, "source": ["### Constructor in Inheritance"]}, {"cell_type": "code", "execution_count": 29, "id": "3e036c44-8537-4e11-bf02-5554b4373570", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def __init__(self):\n", "        print(\"A's init\")\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B(A):\n", "    def __init__(self):\n", "        print(\"B's init\")\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")"]}, {"cell_type": "code", "execution_count": 31, "id": "e3298f82-b87d-46a7-aae5-67defbbd2d8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A's init\n", "B's init\n"]}], "source": ["a = A()\n", "b = B()"]}, {"cell_type": "code", "execution_count": 33, "id": "81cd65a4-8eb3-4a1b-96e6-fb45ef74ed4f", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def __init__(self):\n", "        print(\"A's init\")\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B(A):\n", "    #def __init__(self):\n", "        #print(\"B's init\")\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")"]}, {"cell_type": "code", "execution_count": 34, "id": "da62d636-6dfb-44a3-b12e-ec2ddda019c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A's init\n", "A's init\n"]}], "source": ["a = A()\n", "b = B()"]}, {"cell_type": "code", "execution_count": 35, "id": "de72f9c0-ade1-45ea-8684-53df103bebf4", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def __init__(self):\n", "        print(\"A's init\")\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B(A):\n", "    def __init__(self):\n", "        print(\"B's init\")\n", "        super().__init__()\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")"]}, {"cell_type": "code", "execution_count": 36, "id": "e2765542-d9ec-4680-97ff-be53665d3d6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A's init\n", "B's init\n", "A's init\n"]}], "source": ["a = A()\n", "b = B()"]}, {"cell_type": "code", "execution_count": 39, "id": "dd009a61-9563-4f59-9783-7960863724cc", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def __init__(self):\n", "        print(\"A's init\")\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B():\n", "    def __init__(self):\n", "        print(\"B's init\")\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")\n", "        \n", "class C(A,B):\n", "    def __init__(self):\n", "        print(\"C's init\")\n", "    def operation_5(self):\n", "        print(\"operation_5\")\n", "    def operation_6(self):\n", "        print(\"operation_6\")\n", "        "]}, {"cell_type": "code", "execution_count": 40, "id": "6fc58a7b-a4c0-4a0b-832f-70c0abbc21ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C's init\n"]}], "source": ["c = C()"]}, {"cell_type": "markdown", "id": "aef9ebae-8854-42c2-bc7f-e85793d955fb", "metadata": {}, "source": ["### mro (Method Resolution Order)"]}, {"cell_type": "code", "execution_count": 41, "id": "d39d9bac-955f-4702-b642-da1fc1adcff6", "metadata": {}, "outputs": [], "source": ["class A():\n", "    def __init__(self):\n", "        print(\"A's init\")\n", "    def operation_1(self):\n", "        print(\"operation_1\")\n", "    def operation_2(self):\n", "        print(\"operation_2\")\n", "\n", "class B():\n", "    def __init__(self):\n", "        print(\"B's init\")\n", "    def operation_3(self):\n", "        print(\"operation_3\")\n", "    def operation_4(self):\n", "        print(\"operation_4\")\n", "        \n", "class C(A,B):\n", "    def __init__(self):\n", "        print(\"C's init\")\n", "        super().__init__()\n", "    def operation_5(self):\n", "        print(\"operation_5\")\n", "    def operation_6(self):\n", "        print(\"operation_6\")"]}, {"cell_type": "markdown", "id": "882c87b7-c590-4728-abff-e4d170b2d243", "metadata": {}, "source": ["Method Resolution Protocol makes execute methods call from left to right"]}, {"cell_type": "code", "execution_count": 42, "id": "1c0a2c87-39f1-4cd0-8b26-f5a4df3fa95b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C's init\n", "A's init\n"]}], "source": ["c = C()"]}, {"cell_type": "markdown", "id": "f991774a-760a-49c0-af49-4d81fe1671d2", "metadata": {}, "source": ["# Polymorphism"]}, {"cell_type": "markdown", "id": "4114a41e-5c94-45b4-9980-0ed7f17343e6", "metadata": {}, "source": ["### <PERSON>"]}, {"cell_type": "code", "execution_count": 56, "id": "e912342f-11f9-41d8-99aa-bf37e2f85cb6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Woof!\n", "Meow!\n", "Hello!\n"]}], "source": ["class Dog:\n", "    def speak(self):\n", "        print(\"Woof!\")\n", "\n", "class Cat:\n", "    def speak(self):\n", "        print(\"Meow!\")\n", "\n", "class Human:\n", "    def speak(self):\n", "        print(\"Hello!\")\n", "\n", "def make_it_speak(entity):\n", "    entity.speak()  # Doesn't care what type it is\n", "\n", "# Duck typing in action:\n", "make_it_speak(Dog())     # Output: Woof!\n", "make_it_speak(Cat())     # Output: Meow!\n", "make_it_speak(Human())   # Output: Hello!\n"]}, {"cell_type": "markdown", "id": "6bc94504-2b62-4ea7-a01e-956a63189090", "metadata": {}, "source": ["### Operator Overloading"]}, {"cell_type": "code", "execution_count": 53, "id": "eb74f0f3-4348-4fac-893b-d4622fff2f05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n", "True\n", "2\n"]}], "source": ["class im_1():\n", "    def __init__(self,a):\n", "        self.a = a\n", "\n", "    def __add__(self,other):\n", "        return self.a + other.a\n", "\n", "    def __gt__(self,other):\n", "        return self.a > other.a\n", "        \n", "    def __str__(self):\n", "        return f\"{self.a}\"\n", "\n", "im1 = im_1(2)\n", "im2 = im_1(3)\n", "print(im1+im2)\n", "print(im2>im1)\n", "print(im1)"]}, {"cell_type": "markdown", "id": "2e08b978-49c8-421f-a386-1ab40999cfe9", "metadata": {}, "source": ["### Method Overloading"]}, {"cell_type": "code", "execution_count": 55, "id": "645be154-a12c-4d5e-bf5b-7aaceef66535", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15\n", "10\n"]}], "source": ["## we dont have concept of method overloading , but python provide us concept of unlimited argument in function\n", "def add(*a):\n", "    sum = 0\n", "    for i in a:\n", "        sum += i\n", "    return sum\n", "print(add(1,2,3,4,5))\n", "print(add(1,2,3,4))"]}, {"cell_type": "code", "execution_count": 60, "id": "438d987b-b9fc-4b6e-9dc4-59d6f688aea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n", "3\n", "1\n"]}], "source": ["def add(a = None , b = None , c = None):\n", "    if c != None:\n", "        return a + b + c\n", "    elif b != None:\n", "        return a + b\n", "    else:\n", "        return a\n", "\n", "print(add(1,2,3))\n", "print(add(1,2))\n", "print(add(1))"]}, {"cell_type": "markdown", "id": "7cf668b7-be0d-4029-9fe7-4066211d518d", "metadata": {}, "source": ["### Method Overriding"]}, {"cell_type": "code", "execution_count": 62, "id": "208ae6c3-985b-4caf-8f57-c98008c03b46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It's mototrola\n", "It's samsung\n"]}], "source": ["class my_phone():\n", "    def show(self):\n", "        print(\"It's mototrola\")\n", "\n", "class phone(my_phone):\n", "    def show(self):\n", "        print(\"It's samsung\")\n", "\n", "m = my_phone()\n", "m.show()\n", "n = phone()\n", "n.show()"]}, {"cell_type": "code", "execution_count": 63, "id": "5f804770-cee2-45cf-b678-eabdd178657b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It's mototrola\n", "It's mototrola\n"]}], "source": ["class my_phone():\n", "    def show(self):\n", "        print(\"It's mototrola\")\n", "\n", "class phone(my_phone):\n", "    pass\n", "\n", "m = my_phone()\n", "m.show()\n", "n = phone()\n", "n.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}