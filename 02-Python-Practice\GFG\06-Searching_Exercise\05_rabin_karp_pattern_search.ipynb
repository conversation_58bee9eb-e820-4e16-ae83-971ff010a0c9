{"cells": [{"cell_type": "markdown", "id": "a6bfc8ae", "metadata": {}, "source": ["# Task: <PERSON><PERSON><PERSON><PERSON><PERSON> for Pattern Searching\n", "\n", "## Problem Statement:\n", "Given a string `txt` of length `n` and a pattern `pat` of length `m`, implement the <PERSON><PERSON><PERSON> algorithm to find all the occurrences of the pattern `pat` in the string `txt`. Assume that `n > m`.\n", "\n", "## Steps:\n", "1. Choose a base (e.g., 256 for extended ASCII) and a large prime number `q` to avoid overflow and reduce collisions.\n", "2. Calculate the hash value of the pattern and the first window of the text.\n", "3. Slide the pattern over text one character at a time:\n", "   - Compare the hash value of the current window with the pattern's hash.\n", "   - If the hash values match, compare characters one by one to confirm a match.\n", "   - Recalculate the hash for the next window using a **rolling hash** technique.\n", "4. Print the starting index of each match found.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0ca55688", "metadata": {}, "outputs": [], "source": ["d = 256"]}, {"cell_type": "code", "execution_count": 2, "id": "5d84798e", "metadata": {}, "outputs": [], "source": ["def search(pat, txt, q):\n", "    M = len(pat)\n", "    N = len(txt)\n", "    i = 0\n", "    j = 0\n", "    p = 0\n", "    t = 0\n", "    h = 1\n", "\n", "    for i in range(M-1):\n", "        h = (h * d) % q\n", "\n", "    for i in range(M):\n", "        p = (d * p + ord(pat[i])) % q\n", "        t = (d * t + ord(txt[i])) % q\n", "    \n", "    for i in range(N - M + 1):\n", "        if p == t:\n", "            for j in range(M):\n", "                if txt[i + j] != pat[j]:\n", "                    break\n", "            \n", "            j += 1\n", "            if j == M:\n", "                print(\"Pattern found at index\", i)\n", "        \n", "        if i < N - M:\n", "            t = (d * (t - ord(txt[i]) * h) + ord(txt[i + M])) % q\n", "            if t < 0:\n", "                t = t + q"]}, {"cell_type": "code", "execution_count": 3, "id": "187102d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> found at index 0\n", "<PERSON><PERSON> found at index 10\n"]}], "source": ["txt = 'Geeks for Geeks'\n", "pat = 'Gee<PERSON>'\n", "q = 101\n", "search(pat, txt, q)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}