{"cells": [{"cell_type": "markdown", "id": "21b9fe2e", "metadata": {}, "source": ["# Task: Square Fibonacci Map\n", "\n", "## Problem Statement:\n", "Write a Python program that generates the **first N Fi<PERSON>acci numbers**, computes their **squares**, and stores them in a list using the built-in `map()` function.\n", "\n", "## Steps:\n", "1. **Define a function** to generate the first N Fi<PERSON> numbers.\n", "2. Use the **`map()` function** to apply a lambda that squares each <PERSON><PERSON><PERSON><PERSON> number.\n", "3. **Convert the result to a list** and print or return the squared <PERSON><PERSON><PERSON> numbers.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a531323d", "metadata": {}, "outputs": [], "source": ["def square_<PERSON><PERSON><PERSON><PERSON>(n):\n", "    <PERSON><PERSON><PERSON><PERSON> = [0, 1]\n", "    squared_<PERSON><PERSON><PERSON><PERSON> = [0, 1]\n", "\n", "    for i in range(2, n):\n", "        next_fibonacci = fibonacci[-1] + fibonacci[-2]\n", "        fibonacci.append(next_fibon<PERSON>ci)\n", "        squared_fibonacci.append(next_fibonacci ** 2)\n", "\n", "    return squared_<PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 3, "id": "88d5d04f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 1, 4, 9, 25, 64, 169, 441, 1156]\n"]}], "source": ["squared_fibonacci = square_fibonacci(10)\n", "print(<PERSON>_<PERSON><PERSON><PERSON>)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}