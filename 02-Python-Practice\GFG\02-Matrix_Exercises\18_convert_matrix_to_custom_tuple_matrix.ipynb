{"cells": [{"cell_type": "markdown", "id": "da5956de-503d-413f-a280-2b777010347c", "metadata": {}, "source": ["# Task: Convert Matrix to Custom Tuple Matrix\n", "\n", "## Problem Statement:\n", "Given a matrix (2D list) and a separate list of custom values, convert the matrix into a matrix of tuples by attaching each custom value to the corresponding element in each row.\n", "\n", "### Steps:\n", "1. Iterate over each row in the matrix along with the corresponding custom list using `zip()`.\n", "2. For each row, pair each matrix element with the corresponding value from the custom list using another `zip()`.\n", "3. Convert the paired values into tuples and form a new row.\n", "4. Collect all such rows to build the final tuple matrix.\n", "5. Return the transformed matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2a08c470-f3ab-43d8-8dc9-937306ac7dcd", "metadata": {}, "outputs": [], "source": ["def convert_matrix_to_tuple_matrix(matrix, add_list):\n", "    res = []\n", "    for idx, ele in zip(add_list, matrix):\n", "        for e in ele:\n", "            res.append((idx, e))\n", "    return res"]}, {"cell_type": "code", "execution_count": 2, "id": "24dd63bd-3443-4f1d-bbcf-03b47d1a68e2", "metadata": {}, "outputs": [], "source": ["test_list = [[4, 5, 6], [6, 7, 3], [1, 3, 4]]"]}, {"cell_type": "code", "execution_count": 3, "id": "2b60f90b-b1d9-4f0a-b56a-7e67ded153f5", "metadata": {}, "outputs": [], "source": ["add_list = ['Gfg', 'is', 'best']"]}, {"cell_type": "code", "execution_count": 4, "id": "279b6029-9626-49c4-8d10-9078469d90e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The original list: [[4, 5, 6], [6, 7, 3], [1, 3, 4]]\n"]}], "source": ["print(\"The original list:\", test_list)"]}, {"cell_type": "code", "execution_count": 5, "id": "d1385b7b-231c-46d8-88bb-4a3672e34cca", "metadata": {}, "outputs": [], "source": ["result = convert_matrix_to_tuple_matrix(test_list, add_list)"]}, {"cell_type": "code", "execution_count": 6, "id": "e7f8d1fc-0046-44ee-9e00-7a91c0d087b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matrix after conversion: [('Gfg', 4), ('Gfg', 5), ('Gfg', 6), ('is', 6), ('is', 7), ('is', 3), ('best', 1), ('best', 3), ('best', 4)]\n"]}], "source": ["print(\"Matrix after conversion:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}