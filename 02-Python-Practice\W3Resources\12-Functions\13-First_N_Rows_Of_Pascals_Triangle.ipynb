{"cells": [{"cell_type": "markdown", "id": "d22e8065", "metadata": {}, "source": ["# Task: Print the First n Rows of Pascal's Triangle\n", "\n", "## Problem Statement:\n", "Write a Python function that prints the first **n rows** of **Pascal's Triangle**, where each number is the sum of the two numbers directly above it in the previous row.\n", "\n", "## Steps:\n", "1. Define a function that accepts an integer `n` as input.\n", "2. Initialize the first row of the triangle as `[1]`.\n", "3. Use a loop to generate each subsequent row:\n", "   - Start each new row with `1`.\n", "   - Calculate intermediate values by summing adjacent values from the previous row.\n", "   - End the row with `1`.\n", "4. Print each row after generating it.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2ede69f9", "metadata": {}, "outputs": [], "source": ["def print_pascals_triangle(n):\n", "    triangle = []\n", "\n", "    for row_num in range(n):\n", "        row = [1]\n", "        if triangle:\n", "            last_row = triangle[-1]\n", "            for i in range(1, len(last_row)):\n", "                row.append(last_row[i - 1] + last_row[i])\n", "            row.append(1)\n", "        triangle.append(row)\n", "\n", "    max_width = len('   '.join(map(str, triangle[-1])))\n", "\n", "    for row in triangle:\n", "        row_str = '   '.join(map(str, row))\n", "        print(row_str.center(max_width))"]}, {"cell_type": "code", "execution_count": 2, "id": "fb773b18", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           1           \n", "         1   1         \n", "       1   2   1       \n", "     1   3   3   1     \n", "   1   4   6   4   1   \n", "1   5   10   10   5   1\n"]}], "source": ["print_pascals_triangle(6)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}