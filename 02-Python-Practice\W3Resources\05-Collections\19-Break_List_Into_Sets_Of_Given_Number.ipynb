{"cells": [{"cell_type": "markdown", "id": "4e4cf513", "metadata": {}, "source": ["# Task: Break a List of Integers into Sets of a Given Positive Number\n", "\n", "## Problem Statement:\n", "Write a Python program that takes a list of integers and a positive integer `k`, and determines whether it's possible to divide the list into sets of size `k` such that each set contains consecutive numbers. Return `True` if it's possible, otherwise `False`.\n", "\n", "## Steps:\n", "1. Check if the total number of elements is divisible by `k`. If not, return `False`.\n", "2. Count the frequency of each number using a dictionary or `collections.Counter`.\n", "3. Sort the keys of the counter.\n", "4. Iterate through the sorted numbers:\n", "   - For each number `num`, if its count is greater than 0:\n", "     - Try to form a sequence of `k` consecutive numbers starting from `num`.\n", "     - Decrease their counts in the counter.\n", "     - If any required number is missing or count is insufficient, return `False`.\n", "5. If all numbers are successfully grouped, return `True`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "bef5380c", "metadata": {}, "outputs": [], "source": ["from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "id": "0ca1e9f9", "metadata": {}, "outputs": [], "source": ["def can_divide_into_consecutive_sets(nums, k):\n", "    if len(nums) % k != 0:\n", "        return False\n", "\n", "    count = Counter(nums)\n", "    sorted_nums = sorted(count)\n", "\n", "    for num in sorted_nums:\n", "        while count[num] > 0:\n", "            for i in range(k):\n", "                next_num = num + i\n", "                if count[next_num] <= 0:\n", "                    return False\n", "                count[next_num] -= 1        \n", "    return True"]}, {"cell_type": "code", "execution_count": 3, "id": "43f90a2b", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["can_divide_into_consecutive_sets([1,2,3,3,4,4,5,6], 4)"]}, {"cell_type": "code", "execution_count": 4, "id": "c875c53e", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["can_divide_into_consecutive_sets([1,2,3,4], 3)"]}, {"cell_type": "code", "execution_count": 5, "id": "76915123", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["can_divide_into_consecutive_sets([1,2,3,4,5,6], 3)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}