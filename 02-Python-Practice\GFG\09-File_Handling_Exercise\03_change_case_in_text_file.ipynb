{"cells": [{"cell_type": "markdown", "id": "20d29028", "metadata": {}, "source": ["# Task: Change case of all characters in a `.txt` file using Python\n", "\n", "## Problem Statement:\n", "Given a `.txt` file, the task is to read its contents and modify the case of all characters — either converting all characters to **uppercase** or **lowercase** — and write the updated content back to the same or a different file.\n", "\n", "## Steps:\n", "1. Use the `open()` function to read the content of the original file.\n", "2. Read the file data using `.read()`.\n", "3. Use the `.upper()` or `.lower()` string method to change the case.\n", "4. Open the same or another file in `'w'` mode to write the modified content.\n", "5. Use `.write()` to store the updated text in the file.\n", "6. Optionally use a `with` block to manage file opening and closing safely.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "165d75b1", "metadata": {}, "outputs": [], "source": ["with open('input.txt','r') as input:\n", "    with open('output.txt','a') as output:\n", "        output.write(\"\\nOutput:\\n\")\n", "        for line in input:\n", "            word_list = line.split()\n", "            word_list = [word[0].upper() + word[1:].lower() if word else word for word in word_list]\n", "            output.write(' '.join(word_list) + '\\n')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}