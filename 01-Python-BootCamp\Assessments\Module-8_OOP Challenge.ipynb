{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Object Oriented Programming Challenge\n", "\n", "For this challenge, create a bank account class that has two attributes:\n", "\n", "* owner\n", "* balance\n", "\n", "and two methods:\n", "\n", "* deposit\n", "* withdraw\n", "\n", "As an added requirement, withdrawals may not exceed the available balance.\n", "\n", "Instantiate your class, make several deposits and withdrawals, and test to make sure the account can't be overdrawn."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["class Account:\n", "    def __init__(self,name,amount):\n", "        self.name = name\n", "        self.amount = amount\n", "\n", "    def deposit(self,add):\n", "        self.amount = self.amount + add\n", "        print(\"Deposit Acepted\")\n", "\n", "    def withdraw(self,sub):\n", "        self.amount = self.amount - sub\n", "        if sub < self.amount:\n", "            print(\"<PERSON><PERSON><PERSON> Accepted\")\n", "        else:\n", "            print(\"Funds Unavailable!\")\n", "            \n", "    def __str__(self):\n", "        return f\"Account holder Name: {self.name} \\nAccount holder's balance: {self.amount}\"\n", "    pass"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# 1. Instantiate the class\n", "acct1 = Account('<PERSON>',100)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Account holder Name: <PERSON> \n", "Account holder's balance: 100\n"]}], "source": ["# 2. Print the object\n", "print(acct1)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. Show the account owner attribute\n", "acct1.name"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Show the account balance attribute\n", "acct1.amount"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON><PERSON>\n"]}], "source": ["# 5. Make a series of deposits and withdrawals\n", "acct1.deposit(50)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Funds Unavailable!\n"]}], "source": ["acct1.withdraw(75)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Funds Unavailable!\n"]}], "source": ["# 6. Make a withdrawal that exceeds the available balance\n", "acct1.withdraw(500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Good job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}