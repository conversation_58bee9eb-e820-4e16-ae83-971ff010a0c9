{"cells": [{"cell_type": "markdown", "id": "64e0b0dd", "metadata": {}, "source": ["# Task: Count the Frequency of Matrix Row Length\n", "\n", "## Problem Statement:\n", "Given a matrix (list of lists), the task is to write a Python program to count the frequency of the lengths of its rows.\n", "\n", "### Steps:\n", "1. Iterate through each row of the matrix and record its length.\n", "2. Use a dictionary or collections module to count how many times each row length appears.\n", "3. Return the frequency count as a mapping of row length to its occurrence.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ccb0c589-774e-40b4-8356-784419427572", "metadata": {}, "outputs": [], "source": ["test_list = [[6, 3, 1], [8, 9], [2], [10, 12, 7], [4, 11]]"]}, {"cell_type": "code", "execution_count": 2, "id": "dae9e65f-89d6-4b3a-b14a-d4a92a57d185", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The origial list is : [[6, 3, 1], [8, 9], [2], [10, 12, 7], [4, 11]]\n"]}], "source": ["print(\"The origial list is : \" + str(test_list))"]}, {"cell_type": "code", "execution_count": 3, "id": "3a1f3595-2402-4307-9cd3-993c5c3c10ca", "metadata": {}, "outputs": [], "source": ["res = dict()"]}, {"cell_type": "code", "execution_count": 5, "id": "dcc7294f-924b-4139-a890-e09cccfa9ad2", "metadata": {}, "outputs": [], "source": ["for sub in test_list:\n", "    if len(sub) not in res:\n", "        res[len(sub)] = 1\n", "    else:\n", "        res[len(sub)] += 1"]}, {"cell_type": "code", "execution_count": 6, "id": "80ea8290-2f1a-4435-aa46-7491ed6a1c9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row length frequencies : {3: 2, 2: 2, 1: 1}\n"]}], "source": ["print(\"Row length frequencies : \" + str(res))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}