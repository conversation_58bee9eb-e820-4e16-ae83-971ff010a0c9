{"cells": [{"cell_type": "markdown", "id": "7acf4010", "metadata": {}, "source": ["# Task: Calculate the Edge Cover of a Graph\n", "\n", "## Problem Statement:\n", "Given an undirected graph with **N vertices**, the task is to calculate its **Edge Cover**, i.e., the **minimum number of edges required** such that every vertex in the graph is incident to at least one edge in the cover.\n", "\n", "## Steps:\n", "1. **Represent the graph** using an adjacency list or adjacency matrix.\n", "2. **Initialize an empty set** or list to store the selected edges for the edge cover.\n", "3. **Traverse all vertices** and for each uncovered vertex:\n", "   - Choose any one of its neighbors that is also uncovered.\n", "   - Add the edge between the two to the edge cover set.\n", "   - Mark both vertices as covered.\n", "4. **If a vertex is still uncovered**, select any adjacent edge (if any) to cover it.\n", "5. **Return or print the count and list of selected edges** as the edge cover.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b19cc98c", "metadata": {}, "outputs": [], "source": ["import math"]}, {"cell_type": "code", "execution_count": 2, "id": "e51dc4fd", "metadata": {}, "outputs": [], "source": ["def edgeCover(n):\n", "    result = math.ceil(n / 2.0)\n", "    return result"]}, {"cell_type": "code", "execution_count": 3, "id": "90ed2048", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["if __name__ == \"__main__\":\n", "    n = 5\n", "    print(int(edgeCover(n)))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}