{"cells": [{"cell_type": "markdown", "id": "8df3b710", "metadata": {}, "source": ["# Task: Dict to CSV and Read\n", "\n", "## Problem Statement:\n", "Write a Python program to **write a dictionary to a CSV file**, then **read the same CSV file** and **display its content**.\n", "\n", "## Steps:\n", "1. Create a **Python dictionary** with appropriate keys and values.\n", "2. Use the **csv module** to write the dictionary to a `.csv` file.\n", "3. Open the created CSV file and **read its contents** using `csv.reader()` or `csv.DictReader()`.\n", "4. **Print the contents** row by row.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "7c82b420", "metadata": {}, "outputs": [], "source": ["import csv"]}, {"cell_type": "code", "execution_count": 2, "id": "b7b81c6e", "metadata": {}, "outputs": [], "source": ["csv_columns = ['id','Column1', 'Column2', 'Column3', 'Column4', 'Column5']"]}, {"cell_type": "code", "execution_count": 3, "id": "4e8308f4", "metadata": {}, "outputs": [], "source": ["dict_data = {'id':['1', '2', '3'],\n", "    'Column1':[33, 25, 56],\n", "    'Column2':[35, 30, 30],\n", "    'Column3':[21, 40, 55],\n", "    'Column4':[71, 25, 55],\n", "    'Column5':[10, 10, 40], }"]}, {"cell_type": "code", "execution_count": 4, "id": "acb944b2", "metadata": {}, "outputs": [], "source": ["csv_file = \"temp.csv\""]}, {"cell_type": "code", "execution_count": 5, "id": "04873a96", "metadata": {}, "outputs": [], "source": ["try:\n", "   with open(csv_file, 'w') as csvfile:\n", "       writer = csv.DictWriter(csvfile, fieldnames=csv_columns)\n", "       writer.writeheader()\n", "       for data in dict_data:\n", "           writer.writerow(dict_data)\n", "except IOError:\n", "   print(\"I/O error\")"]}, {"cell_type": "code", "execution_count": 6, "id": "e71a06ba", "metadata": {}, "outputs": [], "source": ["data = csv.DictReader(open(csv_file))"]}, {"cell_type": "code", "execution_count": 7, "id": "50374c4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file as a dictionary:\n", "\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n", "{'id': \"['1', '2', '3']\", 'Column1': '[33, 25, 56]', 'Column2': '[35, 30, 30]', 'Column3': '[21, 40, 55]', 'Column4': '[71, 25, 55]', 'Column5': '[10, 10, 40]'}\n"]}], "source": ["print(\"CSV file as a dictionary:\\n\")\n", "for row in data:\n", "   print(row)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}