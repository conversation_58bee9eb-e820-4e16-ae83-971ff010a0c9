{"cells": [{"cell_type": "markdown", "id": "f88b9da5-bb25-4462-9871-a10094279722", "metadata": {}, "source": ["# Task: Adding and Subtracting Matrices in Python\n", "\n", "## Problem Statement:\n", "Given two matrices of the same dimension, perform element-wise addition and subtraction of the matrices.\n", "\n", "### Steps:\n", "1. Verify that both matrices have the same dimensions.\n", "2. Use nested loops or list comprehension to iterate through each element.\n", "3. For addition, add corresponding elements from both matrices.\n", "4. For subtraction, subtract elements of the second matrix from the first.\n", "5. Return the resulting matrices after addition and subtraction.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e1d4b1e2-f0fb-4610-8e64-5850c4edcd66", "metadata": {}, "outputs": [], "source": ["def add_matrices(mat1, mat2):\n", "    if len(mat1) != len(mat2) or len(mat1[0]) != len(mat2[0]):\n", "        raise ValueError(\"Matrices must have the same dimensions\")\n", "\n", "    result = [[mat1[i][j] + mat2[i][j] for j in range(len(mat1[0]))] for i in range(len(mat1))]\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 2, "id": "9f6a7837-06bc-42e4-9dc5-ecdb9763d536", "metadata": {}, "outputs": [], "source": ["def sub_matrices(mat1, mat2):\n", "    if len(mat1) != len(mat2) or len(mat1[0]) != len(mat2[0]):\n", "        raise ValueError(\"Matrices must have the same dimensions\")\n", "\n", "    result = [[mat1[i][j] + mat2[i][j] for j in range(len(mat1[0]))] for i in range(len(mat1))]\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 3, "id": "4fe9288c-18ae-4436-8fcc-ee0fb147906b", "metadata": {}, "outputs": [], "source": ["A = [[1,2],[3,4]]\n", "B = [[4,5],[6,7]]"]}, {"cell_type": "code", "execution_count": 4, "id": "82f7c28b-e106-4f6d-a44b-c54a45948ad5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[5, 7], [9, 11]]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["add_matrices(A,B)"]}, {"cell_type": "code", "execution_count": 5, "id": "6a9d44ae-f5e6-4dd8-b862-25d3c19<PERSON>ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[5, 7], [9, 11]]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["sub_matrices(A,B)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}