{"cells": [{"cell_type": "markdown", "id": "fe1662fc-b6d7-4d46-b787-09db34c578f5", "metadata": {}, "source": ["# Task: Convert <PERSON><PERSON> Matrix to Tuple List\n", "\n", "## Problem Statement:\n", "Given a tuple matrix, flatten it to a tuple list with each tuple representing each column.\n", "\n", "### Steps:\n", "1. Iterate through each column in the matrix.\n", "2. For each column, extract the elements and group them into a tuple.\n", "3. Collect all the tuples to form the final tuple list.\n", "4. Return the list of tuples where each tuple corresponds to a column in the original matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8232c0f5-5a9f-48b0-bd97-462906d7c3d7", "metadata": {}, "outputs": [], "source": ["def convert_tuple_matrix_to_tuple_list(test_list):\n", "    temp = [ele for sub in test_list for ele in sub]\n", "    return list(zip(*temp))"]}, {"cell_type": "code", "execution_count": 2, "id": "390016ea-48df-4c54-a322-8c55a6500799", "metadata": {}, "outputs": [], "source": ["test_list = [[(4, 5), (7, 8)], [(10, 13), (18, 17)], [(0, 4), (10, 1)]]"]}, {"cell_type": "code", "execution_count": 3, "id": "7a548e15-64c8-4823-adb1-0d534c09c2f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The converted tuple list : [(4, 7, 10, 18, 0, 10), (5, 8, 13, 17, 4, 1)]\n"]}], "source": ["print(\"The converted tuple list : \" + str(convert_tuple_matrix_to_tuple_list(test_list)))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}