{"cells": [{"cell_type": "markdown", "id": "3a8eaed4", "metadata": {}, "source": ["# Task: Comb Sort - Python\n", "\n", "## Problem Statement:\n", "Implement **Comb Sort**, an improved version of Bubble Sort that uses a shrinking gap strategy to eliminate small elements near the end more efficiently. This algorithm repeatedly compares elements at a certain gap and reduces the gap until it becomes 1.\n", "\n", "## Steps:\n", "1. **Initialize the gap** as the length of the list.\n", "2. Define a **shrink factor** (commonly 1.3).\n", "3. Repeat the following until the gap becomes 1 and no swaps are made:\n", "   - Update the gap by dividing it by the shrink factor (use `int(gap / shrink_factor)`).\n", "   - Ensure the minimum gap is 1.\n", "   - Compare elements `arr[i]` and `arr[i + gap]`:\n", "     - If out of order, swap them and set `swapped = True`.\n", "4. If the final pass is made with gap = 1 and no swaps occur, the list is sorted.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "41f6ba22", "metadata": {}, "outputs": [], "source": ["def getNextGap(gap):\n", "    gap = (gap * 10) // 13\n", "    if gap < 1:\n", "        return 1\n", "    return gap"]}, {"cell_type": "code", "execution_count": 2, "id": "460ec587", "metadata": {}, "outputs": [], "source": ["def combSort(arr):\n", "    n = len(arr)\n", "    gap = n\n", "    swapped = True\n", "    while gap != 1 or swapped == True:\n", "        gap = getNextGap(gap)\n", "        swapped = False\n", "        for i in range(0, n - gap):\n", "            if arr[i] > arr[i + gap]:\n", "                arr[i], arr[i + gap] = arr[i + gap], arr[i]\n", "                swapped = True"]}, {"cell_type": "code", "execution_count": 3, "id": "d2c85eef", "metadata": {}, "outputs": [], "source": ["arr = [8, 4, 1, 3, -44, 23, -6, 28, 0]\n", "combSort(arr)"]}, {"cell_type": "code", "execution_count": null, "id": "ee8c3a19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sorted array:\n", "-44 -6 0 1 3 4 8 23 28 "]}], "source": ["print(\"Sorted array:\")\n", "for i in range(len(arr)):\n", "    print(arr[i], end=\" \")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}