{"cells": [{"cell_type": "markdown", "id": "a57f8528-2205-4f9b-b026-2f6a20218821", "metadata": {}, "source": ["# Task: Create an n x n Square Matrix with Even Opposite Corner Sums in All Sub-Matrices\n", "\n", "## Problem Statement:\n", "Given an integer `n`, generate an `n x n` matrix with distinct elements from 1 to `n^2`, such that for every possible sub-matrix, the sum of opposite corner elements is even.\n", "\n", "### Steps:\n", "1. Initialize a 2D matrix with numbers from 1 to `n^2` in row-major order.\n", "2. To ensure that the sum of opposite corners is even, arrange the diagonals to consist of either all odd or all even numbers.\n", "3. For **odd n**, the natural sequential arrangement already satisfies this condition, so no further modification is needed.\n", "4. For **even n**, reverse every alternate row to align the diagonals with all even or all odd numbers, ensuring every submatrix meets the even-sum condition on both diagonals.\n", "5. Return the final matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "45cea204-e4d8-4e24-a159-ec39ba08fae3", "metadata": {}, "outputs": [], "source": ["import itertools"]}, {"cell_type": "code", "execution_count": 2, "id": "ec5f32a1-a9fd-4f81-b603-069263e8fccc", "metadata": {}, "outputs": [], "source": ["def sub_mat_even(n):\n", "    temp = itertools.count(1)\n", "    l = [[next(temp) for i in range(n)] for i in range(n)]\n", "    \n", "    if n % 2 == 0:\n", "        for i in range(0, len(l)):\n", "            if i % 2 == 1:\n", "                l[i][:] = l[i][::-1]\n", "    \n", "    for i in range(n):\n", "        for j in range(n):\n", "            print(l[i][j], end=\" \")\n", "        print()"]}, {"cell_type": "code", "execution_count": 3, "id": "445c7683-f1ce-44c7-a627-8275447a56e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 2 3 4 \n", "8 7 6 5 \n", "9 10 11 12 \n", "16 15 14 13 \n"]}], "source": ["n = 4\n", "sub_mat_even(n)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}