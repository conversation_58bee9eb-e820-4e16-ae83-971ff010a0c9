{"cells": [{"cell_type": "markdown", "id": "0b3fd47d", "metadata": {}, "source": ["# Task: Future Date Filter\n", "\n", "## Problem Statement:\n", "Write a Python program to **filter out future dates** from a list of date strings (in the format `\"YYYY-MM-DD\"`) using the **`filter()` function**. Only dates that are **today or in the past** should be included in the result.\n", "\n", "## Steps:\n", "1. **Import** the `datetime` module to handle date operations.\n", "2. Create a list of **date strings** in `\"YYYY-MM-DD\"` format.\n", "3. Define a **filtering function** that:\n", "   - Parses the date string using `datetime.strptime`.\n", "   - Compares it with today's date using `datetime.date.today()`.\n", "4. Use the **`filter()` function** to apply this logic.\n", "5. **Return or print** the filtered list of valid (non-future) dates.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "37db5ca8", "metadata": {}, "outputs": [], "source": ["from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "id": "8f9cc81e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["List of dates:\n", "['2023-07-11', '2022-02-22', '2024-05-11', '2025-12-31', '2021-01-01']\n"]}], "source": ["date_strings = [\"2023-07-11\", \"2022-02-22\", \"2024-05-11\", \"2025-12-31\", \"2021-01-01\"]\n", "print(\"List of dates:\")\n", "print(date_strings)"]}, {"cell_type": "code", "execution_count": 3, "id": "38fd5df2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current date: 2025-07-24 10:01:29.026066\n"]}], "source": ["dates = [datetime.strptime(date, \"%Y-%m-%d\") for date in date_strings]\n", "current_date = datetime.now()\n", "print(\"Current date:\", current_date)"]}, {"cell_type": "code", "execution_count": 4, "id": "6f0ef9dd", "metadata": {}, "outputs": [], "source": ["def is_date_in_future(date):\n", "    return date > current_date"]}, {"cell_type": "code", "execution_count": 5, "id": "b15e6bae", "metadata": {}, "outputs": [], "source": ["dates_in_past = list(filter(is_date_in_future, dates))\n", "filtered_date_strings = [date.strftime(\"%Y-%m-%d\") for date in dates_in_past]"]}, {"cell_type": "code", "execution_count": 6, "id": "c800d34d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dates in the future:\n", "['2025-12-31']\n"]}], "source": ["print(\"Dates in the future:\")\n", "print(filtered_date_strings)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}