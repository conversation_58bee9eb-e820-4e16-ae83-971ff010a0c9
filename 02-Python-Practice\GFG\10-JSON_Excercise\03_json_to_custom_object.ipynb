{"cells": [{"cell_type": "markdown", "id": "fa382197", "metadata": {}, "source": ["# Task: Convert JSON Data Into a Custom Python Object\n", "\n", "## Problem Statement:\n", "Given a JSON string or file, the task is to convert its data into a custom Python object with attributes corresponding to the keys in the JSON data.\n", "\n", "## Steps:\n", "1. **Define a custom Python class** with an `__init__()` method that maps JSON keys to object attributes.\n", "2. **Load the JSON data** using `json.loads()` (for string) or `json.load()` (for file).\n", "3. **Use the custom class** to convert the dictionary into an object using either `**kwargs` unpacking or a custom decoder.\n"]}, {"cell_type": "markdown", "id": "ecca0e00", "metadata": {}, "source": ["## Using namedtuple for Custom Objects"]}, {"cell_type": "code", "execution_count": 1, "id": "649edbd4", "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import namedtuple"]}, {"cell_type": "code", "execution_count": 2, "id": "bed6f134", "metadata": {}, "outputs": [], "source": ["data = '{\"name\": \"<PERSON><PERSON>\", \"id\": 1, \"location\": \"Mumbai\"}'"]}, {"cell_type": "code", "execution_count": 3, "id": "033c4e14", "metadata": {}, "outputs": [], "source": ["x = json.loads(data, object_hook=lambda d: namedtuple('X', d.keys())(*d.values()))"]}, {"cell_type": "code", "execution_count": 4, "id": "a52a7a06", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Geek 1 Mumbai\n"]}], "source": ["print(x.name, x.id, x.location)"]}, {"cell_type": "markdown", "id": "4ac42126", "metadata": {}, "source": ["## Using SimpleNamespace for Simplicity"]}, {"cell_type": "code", "execution_count": 5, "id": "a3b8e49f", "metadata": {}, "outputs": [], "source": ["import json\n", "from types import SimpleNamespace as Namespace"]}, {"cell_type": "code", "execution_count": 6, "id": "face2ab6", "metadata": {}, "outputs": [], "source": ["data = '{\"name\": \"GeekNamespace\", \"id\": 2, \"location\": \"Bangalore\"}'"]}, {"cell_type": "code", "execution_count": 7, "id": "e61e5ac6", "metadata": {}, "outputs": [], "source": ["x = json.loads(data, object_hook=lambda d: Namespace(**d))"]}, {"cell_type": "code", "execution_count": 8, "id": "5b3c59f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GeekNamespace 2 Bangalore\n"]}], "source": ["print(x.name, x.id, x.location)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}