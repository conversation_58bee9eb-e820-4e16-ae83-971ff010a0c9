{"cells": [{"cell_type": "markdown", "id": "2d5c5618-7b99-477a-861e-d291386a2910", "metadata": {}, "source": ["# Task: Validate an IP Address in Python Without Using RegEx\n", "\n", "## Problem Statement:\n", "Given a string representing an IP address, write a Python program to validate whether it is a proper IPv4 address without using regular expressions.\n", "\n", "### Steps:\n", "1. Use the `count()` method to ensure the IP address contains exactly three periods (`.`).\n", "2. Split the string using `split('.')` to get individual segments.\n", "3. Check if there are exactly four segments after splitting.\n", "4. For each segment:\n", "   - Ensure it contains only digits.\n", "   - Convert it to an integer and check if it lies between 0 and 255.\n", "   - Ensure there are no leading zeros (e.g., '01' is invalid).\n", "5. If all segments pass these checks, the IP address is valid; otherwise, it's invalid.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3638028a-690f-4bd9-99bc-f2ed90e42689", "metadata": {}, "outputs": [], "source": ["def isValidIP(ip):\n", "    if ip.count('.') != 3:\n", "        return 'Invalid IP!'\n", "\n", "    l = list(map(str,ip.split('.')))\n", "\n", "    for ele in l:\n", "        if int(ele) < 0 or int(ele) > 255 or (ele[0]=='0' and len(ele)!=1):\n", "            return 'Invalid Ip address'\n", "\n", "    return 'Valid <PERSON>p address'"]}, {"cell_type": "code", "execution_count": 2, "id": "d1d62378-b901-49f1-a195-2dd23d31df71", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valid Ip address\n"]}], "source": ["print(isValidIP('***********'))"]}, {"cell_type": "code", "execution_count": 3, "id": "2ad52f15-44fa-471b-9e48-b21a4f48024b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Invalid Ip address\n"]}], "source": ["print(isValidIP('666.1.2.2'))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}