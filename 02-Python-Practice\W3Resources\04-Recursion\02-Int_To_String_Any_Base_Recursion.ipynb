{"cells": [{"cell_type": "markdown", "id": "662191ec", "metadata": {}, "source": ["# Task: Integer to String Conversion in Any Base Using Recursion\n", "\n", "## Problem Statement:\n", "Write a Python program that converts a **given integer** into a **string representation** in any base (from base 2 to base 16), using **recursive logic**. This helps in understanding how numbers are built in different bases.\n", "\n", "## Steps:\n", "1. Define a **string of valid characters** (e.g., `'0123456789ABCDEF'`) for digit mapping.\n", "2. Create a **recursive function** that:\n", "   - Divides the number by the base to reduce the problem size.\n", "   - Gets the remainder and converts it to the corresponding character.\n", "   - Appends characters in the correct order as the recursive stack unwinds.\n", "3. Handle the **base case** where the number is less than the base (single digit).\n", "4. Call the function with the given number and base, and print the result.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c39b529e", "metadata": {}, "outputs": [], "source": ["def convert_to_any_base(num, base):\n", "    if num < 0 or base < 2 or base > 36:\n", "        return \"Invalid input\"\n", "    \n", "    digits = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\"\n", "    \n", "    if num == 0:\n", "        return \"0\"\n", "    \n", "    if num < base:\n", "        return digits[num]\n", "    \n", "    return convert_to_any_base(num // base, base) + digits[num % base]"]}, {"cell_type": "code", "execution_count": 2, "id": "3c1ec301", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1010'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 2)"]}, {"cell_type": "code", "execution_count": 3, "id": "6c5c3fe6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'12'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 8)"]}, {"cell_type": "code", "execution_count": 4, "id": "f9d50304", "metadata": {}, "outputs": [{"data": {"text/plain": ["'A'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 16)"]}, {"cell_type": "code", "execution_count": 5, "id": "b4e68c0d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'A'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 36)"]}, {"cell_type": "code", "execution_count": 6, "id": "58ffe26c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Invalid input'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 1)"]}, {"cell_type": "code", "execution_count": 7, "id": "4ed80b67", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Invalid input'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["convert_to_any_base(10, 37)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}