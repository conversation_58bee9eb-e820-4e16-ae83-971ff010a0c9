from datetime import datetime
import pytz
import time

local_time = datetime.now()
local_tz = time.tzname
utc_offset = time.timezone
dst_offset = time.altzone if time.daylight else time.timezone

print("Local Time:", local_time.strftime("%Y-%m-%d %H:%M:%S"))
print("Timezone Abbreviations:", local_tz)
print("UTC Offset (seconds):", -utc_offset)
print("DST Offset (seconds):", -dst_offset)

def print_timezone_info(tz_name):
    tz = pytz.timezone(tz_name)
    now = datetime.now(tz)
    print(f"\nTimezone: {tz_name}")
    print("Time: ",now.strftime("%Y-%m-%d %H:%M:%S"))
    print("Abbreviation: ",now.tzname())
    print("UTC Offset: ",now.utcoffset())

timezones = ['UTC', 'US/Eastern', 'Asia/Kolkata', 'Europe/London']

for tz in timezones:
    print_timezone_info(tz)