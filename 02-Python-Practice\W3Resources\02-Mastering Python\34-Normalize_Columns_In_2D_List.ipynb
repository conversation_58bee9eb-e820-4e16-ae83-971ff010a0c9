{"cells": [{"cell_type": "markdown", "id": "e6a66f77", "metadata": {}, "source": ["# Task: Normalize Values in Each Column of a 2D List\n", "\n", "## Problem Statement:\n", "Write a Python program to **normalize the values in each column** of a 2D list (matrix). Normalization here means scaling the values in each column to a **range between 0 and 1** using the formula:\n", "\n", "\\[\n", "\\text{normalized\\_value} = \\frac{(x - \\min)}{(\\max - \\min)}\n", "\\]\n", "\n", "## Steps:\n", "1. **Transpose the 2D list** to work with columns as rows.\n", "2. For each column:\n", "   - Compute the **minimum and maximum values**.\n", "   - Apply the normalization formula to each value.\n", "3. **Transpose back** to get the final normalized 2D list in original row-column format.\n", "4. **Return or print** the normalized matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c35fc3c2", "metadata": {}, "outputs": [], "source": ["import random"]}, {"cell_type": "code", "execution_count": 2, "id": "15eacd2e", "metadata": {}, "outputs": [], "source": ["matrix = [[random.randint(0,100) for _ in range(3)] for _ in range(4)]"]}, {"cell_type": "code", "execution_count": 3, "id": "e5b0beb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[35, 88, 90], [97, 75, 70], [49, 4, 98], [29, 40, 7]]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["matrix"]}, {"cell_type": "code", "execution_count": 4, "id": "fe5cbf1b", "metadata": {}, "outputs": [], "source": ["normalized_matrix = [[(x - min(col)) / (max(col) - min(col)) for x in col] for col in zip(*matrix)]"]}, {"cell_type": "code", "execution_count": 5, "id": "fdf6dde1", "metadata": {}, "outputs": [], "source": ["normalized_matrix = list(map(list, zip(*normalized_matrix)))"]}, {"cell_type": "code", "execution_count": 6, "id": "d513c13e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[0.08823529411764706, 1.0, 0.9120879120879121],\n", " [1.0, 0.8452380952380952, 0.6923076923076923],\n", " [0.29411764705882354, 0.0, 1.0],\n", " [0.0, 0.42857142857142855, 0.0]]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["normalized_matrix"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}