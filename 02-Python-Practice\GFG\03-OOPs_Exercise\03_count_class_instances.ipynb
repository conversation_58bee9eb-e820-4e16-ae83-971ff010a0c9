{"cells": [{"cell_type": "markdown", "id": "f1b2267d-4b4f-4f9c-b5fd-e4b857e71bd8", "metadata": {}, "source": ["# Task: Count Number of Instances of a Class in Python\n", "\n", "## Problem Statement:\n", "Determine how many instances (objects) of a class have been created in Python during runtime.\n", "\n", "### Steps:\n", "1. Define a class with a class-level variable (e.g., `instance_count`) initialized to 0.\n", "2. Inside the `__init__()` constructor, increment this variable each time an object is created.\n", "3. The constructor is automatically called on object creation, so it serves as a counter trigger.\n", "4. Access the class-level variable to get the current number of instances created.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4821a29c-99e7-40e7-bc55-4e1ff83d968b", "metadata": {}, "outputs": [], "source": ["class instance_count():\n", "    counter = 0\n", "\n", "    def __init__(self):\n", "        instance_count.counter += 1"]}, {"cell_type": "code", "execution_count": 2, "id": "c233696e-5b2f-423a-b9b8-a11453fb71c8", "metadata": {}, "outputs": [], "source": ["i1 = instance_count()"]}, {"cell_type": "code", "execution_count": 3, "id": "e0b007ce-18c9-44a8-8bf4-a1c3a484008d", "metadata": {}, "outputs": [], "source": ["i2 = instance_count()"]}, {"cell_type": "code", "execution_count": 4, "id": "c885538f-9c2a-45f9-aec4-18efedc5d9ef", "metadata": {}, "outputs": [], "source": ["i3 = instance_count()"]}, {"cell_type": "code", "execution_count": 5, "id": "60804986-b5e8-4c7b-a72a-cad4b891ddd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["print(instance_count.counter)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}