{"cells": [{"cell_type": "markdown", "id": "b2567460", "metadata": {}, "source": ["# Task: Check Whether a JSON String Contains a Complex Object\n", "\n", "## Problem Statement:\n", "Write a Python program to determine whether a given JSON string contains any complex Python objects (e.g., datetime, custom classes, etc.) that are not natively serializable by the default JSON encoder.\n", "\n", "## Steps:\n", "1. Import the `json` module.\n", "2. Attempt to parse the JSON string using `json.loads()`.\n", "3. If the string is successfully parsed, iterate over the values to check for types like `dict`, `list`, etc.\n", "4. Define complex objects as non-primitive types (e.g., not `int`, `float`, `str`, `bool`, `NoneType`).\n", "5. Return `True` if any complex object is found, otherwise `False`.\n", "6. Use `try-except` to catch `json.JSONDecodeError` for invalid JSON strings.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "33ae6ccd", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 2, "id": "d275a9eb", "metadata": {}, "outputs": [], "source": ["def contains_complex_object(json_str):\n", "    try:\n", "        data = json.loads(json_str)\n", "    except json.JSONDecoderError:\n", "        return False\n", "\n", "    def isComplex(value):\n", "        if isinstance(value, (dict, list)):\n", "            if isinstance(value, dict):\n", "                return any(isComplex(v) for v in value.values())\n", "            return any(isComplex(item) for item in value)\n", "        return not isinstance(value, (str, int, float, bool, type(None)))\n", "\n", "    return isComplex(data)"]}, {"cell_type": "code", "execution_count": 3, "id": "17ca0155", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["json_str1 = '{\"name\": \"<PERSON>\", \"age\": 30, \"active\": true}'\n", "contains_complex_object(json_str1)"]}, {"cell_type": "code", "execution_count": 4, "id": "af7ffa4d", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["json_str2 = '{\"user\": {\"name\": \"<PERSON>\", \"details\": [1, 2, {\"extra\": null}]}}'\n", "contains_complex_object(json_str2)"]}, {"cell_type": "code", "execution_count": 5, "id": "a8ac1271", "metadata": {}, "outputs": [], "source": ["# json_str3 = '{\"name\": \"<PERSON>\", \"age\": 30, \"created_at\": datetime.now()}'\n", "# contains_complex_object(json_str3)\n", "\n", "# # False due to JSONDecodeError"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}