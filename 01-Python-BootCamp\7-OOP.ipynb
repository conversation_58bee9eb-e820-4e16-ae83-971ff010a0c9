{"cells": [{"cell_type": "markdown", "id": "0e9d9553", "metadata": {}, "source": ["# Module 8: Object Oriented Programming"]}, {"cell_type": "code", "execution_count": 1, "id": "a90329e5", "metadata": {}, "outputs": [], "source": ["mylist = [1,2,3]"]}, {"cell_type": "code", "execution_count": 3, "id": "185865da", "metadata": {}, "outputs": [{"data": {"text/plain": ["(set, list)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["myset = set()\n", "\n", "type(myset),type(mylist)"]}, {"cell_type": "code", "execution_count": 25, "id": "15321979", "metadata": {}, "outputs": [], "source": ["class Dog():\n", "    def __init__(self,breed,name):\n", "        self.breed = breed\n", "        self.name = name\n", "    \n", "    # Methods\n", "    def bark(self):\n", "        print(f'My name is {self.name} and I am barking!')\n"]}, {"cell_type": "code", "execution_count": 26, "id": "83afa344", "metadata": {}, "outputs": [], "source": ["my_dog = Dog(breed='<PERSON>',name='<PERSON>')"]}, {"cell_type": "code", "execution_count": 27, "id": "2cd2fd76", "metadata": {}, "outputs": [{"data": {"text/plain": ["__main__.<PERSON>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["type(my_dog)"]}, {"cell_type": "code", "execution_count": 28, "id": "6b3c2eb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["('<PERSON>', '<PERSON>')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dog.breed,my_dog.name"]}, {"cell_type": "code", "execution_count": 29, "id": "6f1b910a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["My name is <PERSON> and I am barking!\n"]}], "source": ["my_dog.bark()"]}, {"cell_type": "code", "execution_count": null, "id": "b8a040ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3.14, 30, 2826.0, 188.4)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["class Circle():\n", "    pi = 3.14\n", "    def __init__(self,radius=1):\n", "        self.radius = radius\n", "        self.area = radius*radius*Circle.pi\n", "    def get_circumference(self):\n", "        return self.radius * Circle.pi * 2\n", "    \n", "\n", "\n", "my_circle = Circle(30)\n", "\n", "my_circle.pi,my_circle.radius,my_circle.area,my_circle.get_circumference()"]}, {"cell_type": "markdown", "id": "1c435ae1", "metadata": {}, "source": ["## Inheritance"]}, {"cell_type": "code", "execution_count": 31, "id": "d913eedb", "metadata": {}, "outputs": [], "source": ["class Animal():\n", "    def __init__(self):\n", "        print(\"Animal Created\")\n", "    \n", "    def who_am_i(self):\n", "        print(\"I am an animal\")\n", "    \n", "    def eat(self):\n", "        print(\"I am eating\")"]}, {"cell_type": "code", "execution_count": 32, "id": "bbfcd791", "metadata": {}, "outputs": [], "source": ["class Dog(Animal):\n", "    def __init__(self):\n", "        Animal.__init__(self)\n", "        print(\"Dog Created\")\n", "    \n", "    def who_am_i(self):\n", "        print(\"I am a dog\")\n", "    \n", "    def bark(self):\n", "        print(\"Woof!\")"]}, {"cell_type": "code", "execution_count": 33, "id": "63b679c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Animal Created\n", "Dog Created\n"]}], "source": ["mydog = Dog()"]}, {"cell_type": "code", "execution_count": 34, "id": "316cdc2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am a dog\n"]}], "source": ["mydog.who_am_i()"]}, {"cell_type": "code", "execution_count": 35, "id": "d9f8d781", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Woof!\n"]}], "source": ["mydog.bark()"]}, {"cell_type": "markdown", "id": "c1fd0b50", "metadata": {}, "source": ["## Polymorphism"]}, {"cell_type": "code", "execution_count": 36, "id": "b3fd8f01", "metadata": {}, "outputs": [], "source": ["class Dog():\n", "    def __init__(self,name):\n", "        self.name = name\n", "    \n", "    def speak(self):\n", "        return self.name + \" says woof!\""]}, {"cell_type": "code", "execution_count": 37, "id": "cdbcfbb1", "metadata": {}, "outputs": [], "source": ["class Cat():\n", "    def __init__(self,name):\n", "        self.name = name\n", "    \n", "    def speak(self):\n", "        return self.name + \" says meow!\""]}, {"cell_type": "code", "execution_count": 39, "id": "ed4025c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> says woof!\n", "<PERSON> says meow!\n"]}], "source": ["niko = Dog(\"Niko\")\n", "felix = <PERSON>(\"<PERSON>\")\n", "\n", "print(niko.speak())\n", "print(felix.speak())"]}, {"cell_type": "code", "execution_count": 40, "id": "c8f9d42f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> says woof!\n", "<PERSON> says meow!\n"]}], "source": ["for pet in [niko,felix]:\n", "    print(pet.speak())"]}, {"cell_type": "code", "execution_count": null, "id": "3cd0bc70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> says woof!\n", "<PERSON> says meow!\n"]}], "source": ["def pet_speak(pet):\n", "    print(pet.speak())\n", "\n", "pet_speak(niko)\n", "pet_speak(felix)"]}, {"cell_type": "code", "execution_count": 42, "id": "041ce128", "metadata": {}, "outputs": [], "source": ["class Animal():\n", "    def __init__(self,name):\n", "        self.name = name\n", "    \n", "    def speak(self):\n", "        raise NotImplementedError(\"Subclass must implement this abstract method\")"]}, {"cell_type": "code", "execution_count": 43, "id": "555b036f", "metadata": {}, "outputs": [], "source": ["class Dog(Animal):\n", "    def speak(self):\n", "        return self.name + \" says woof!\""]}, {"cell_type": "code", "execution_count": 44, "id": "10d81c20", "metadata": {}, "outputs": [], "source": ["class Cat(Animal):\n", "    def speak(self):\n", "        return self.name + \" says meow!\""]}, {"cell_type": "code", "execution_count": null, "id": "a7135108", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON> says meow!'"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["fido = Dog(\"Fido\")\n", "isis = Cat(\"Isis\")"]}, {"cell_type": "code", "execution_count": 46, "id": "f65fc883", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON> says woof!'"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["fido.speak()"]}, {"cell_type": "code", "execution_count": 47, "id": "5ec6b11f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON> says meow!'"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["isis.speak()"]}, {"cell_type": "markdown", "id": "ca87a097", "metadata": {}, "source": ["## Special MagicDunder Methods"]}, {"cell_type": "code", "execution_count": 50, "id": "6c141f06", "metadata": {}, "outputs": [], "source": ["class Book():\n", "    def __init__(self,title,author,pages):\n", "        self.title = title\n", "        self.author = author\n", "        self.pages = pages\n", "    \n", "    def __str__(self):\n", "        return f\"{self.title} by {self.author}\"\n", "    \n", "    def __len__(self):\n", "        return self.pages\n", "    \n", "    def __del__(self):\n", "        print(\"A book object has been deleted\")"]}, {"cell_type": "code", "execution_count": 53, "id": "f4d59123", "metadata": {}, "outputs": [], "source": ["b = Book(\"Python Rocks!\",\"Jose\",200)"]}, {"cell_type": "code", "execution_count": 54, "id": "e9c62e7c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python Rocks! by <PERSON>\n"]}], "source": ["print(b)\n"]}, {"cell_type": "code", "execution_count": 55, "id": "bc7ebbf4", "metadata": {}, "outputs": [{"data": {"text/plain": ["200"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["len(b)"]}, {"cell_type": "code", "execution_count": 56, "id": "9dbbe584", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A book object has been deleted\n"]}], "source": ["del b"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}