{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Function Practice Exercises\n", "\n", "Problems are arranged in increasing difficulty:\n", "* Warmup - these can be solved using basic comparisons and methods\n", "* Level 1 - these may involve if/then conditional statements and simple methods\n", "* Level 2 - these may require iterating over sequences, usually with some kind of loop\n", "* Challenging - these will take some creativity to solve"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## WARMUP SECTION:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### LESSER OF TWO EVENS: Write a function that returns the lesser of two given numbers *if* both numbers are even, but returns the greater if one or both numbers are odd\n", "    lesser_of_two_evens(2,4) --> 2\n", "    lesser_of_two_evens(2,5) --> 5"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "5\n"]}], "source": ["def lesser_of_two_evens(a,b):\n", "    if a % 2 == 0 and b % 2 == 0:\n", "        return min(a,b)\n", "    else:\n", "        return max(a,b)\n", "print(lesser_of_two_evens(2,4))\n", "print(lesser_of_two_evens(2,5))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "lesser_of_two_evens(2,4)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "lesser_of_two_evens(2,5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### ANIMAL CRACKERS: Write a function takes a two-word string and returns True if both words begin with same letter\n", "    animal_crackers('Levelheaded Llama') --> True\n", "    animal_crackers('Crazy Kangaroo') --> False"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def animal_crackers(text):\n", "    list_1 = text.split()\n", "    if list_1[0][0] == list_1[1][0]:\n", "        return True\n", "    else:\n", "        return False\n", "    "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "animal_crackers('Levelheaded Llama')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "animal_crackers('Crazy Kangaroo')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### MAKES TWENTY: Given two integers, return True if the sum of the integers is 20 *or* if one of the integers is 20. If not, return False\n", "\n", "    makes_twenty(20,10) --> True\n", "    makes_twenty(12,8) --> True\n", "    makes_twenty(2,3) --> False"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def makes_twenty(n1,n2):\n", "    if n1 == 20 or n2 == 20 or n1+n2 == 20:\n", "        return True\n", "    else:\n", "        return False\n", "    pass"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "makes_twenty(20,10)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "makes_twenty(2,3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LEVEL 1 PROBLEMS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### OLD MACDONALD: Write a function that capitalizes the first and fourth letters of a name\n", "     \n", "    old_mac<PERSON><PERSON>('macdonald') --> <PERSON>\n", "    \n", "Note: `'mac<PERSON><PERSON>'.capitalize()` returns `'<PERSON>'`"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def old_mac<PERSON>ald(name): \n", "    return name[0].upper() + name[1:3] + name[3].upper() + name[4:]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check\n", "old_mac<PERSON><PERSON>('macdonald')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### MASTER YODA: Given a sentence, return a sentence with the words reversed\n", "\n", "    master_yoda('I am home') --> 'home am I'\n", "    master_yoda('We are ready') --> 'ready are We'\n", "    \n", "Note: The .join() method may be useful here. The .join() method allows you to join together strings in a list with some connector string. For example, some uses of the .join() method:\n", "\n", "    >>> \"--\".join(['a','b','c'])\n", "    >>> 'a--b--c'\n", "\n", "This means if you had a list of words you wanted to turn back into a sentence, you could just join them with a single space string:\n", "\n", "    >>> \" \".join(['Hello','world'])\n", "    >>> \"Hello world\""]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["def master_yoda(text):\n", "    list_1 = text.split()\n", "    lidt_1 = list_1.reverse()\n", "    ans = \"\"\n", "    for i in list_1:\n", "        ans += i\n", "        ans += \" \"\n", "        \n", "    print(ans)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["home am I \n"]}], "source": ["# Check\n", "master_yoda('I am home')"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ready are We \n"]}], "source": ["# Check\n", "master_yoda('We are ready')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### ALMOST THERE: Given an integer n, return True if n is within 10 of either 100 or 200\n", "\n", "    almost_there(90) --> True\n", "    almost_there(104) --> True\n", "    almost_there(150) --> False\n", "    almost_there(209) --> True\n", "    \n", "NOTE: `abs(num)` returns the absolute value of a number"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def almost_there(n):\n", "    if (90 <= n <= 110) or (190 <= n <= 210):\n", "        print(\"True\")\n", "    else:\n", "        print(\"False\")"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# Check\n", "almost_there(104)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Check\n", "almost_there(150)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# Check\n", "almost_there(209)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LEVEL 2 PROBLEMS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### FIND 33: \n", "\n", "Given a list of ints, return True if the array contains a 3 next to a 3 somewhere.\n", "\n", "    has_33([1, 3, 3]) → True\n", "    has_33([1, 3, 1, 3]) → False\n", "    has_33([3, 1, 3]) → False"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["def has_33(nums):\n", "    flag = 0\n", "    for i,j in enumerate(nums):\n", "        if i >= 1:\n", "            if j == 3 and nums[i-1] == 3:\n", "                print(\"True\")\n", "                flag = 1 \n", "                break\n", "    if flag == 0:\n", "        print(\"False\")"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# Check\n", "has_33([1, 3, 3])"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Check\n", "has_33([1, 3, 1, 3])"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Check\n", "has_33([3, 1, 3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### PAPER DOLL: Given a string, return a string where for every character in the original there are three characters\n", "    paper_doll('Hello') --> 'HHHeeellllllooo'\n", "    paper_doll('Mississippi') --> 'MMMiiissssssiiippppppiii'"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["def paper_doll(text):\n", "    ans = \"\"\n", "    for i in text:\n", "        for j in range(3):\n", "            ans += i\n", "    print(ans)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HHHeeellllllooo\n"]}], "source": ["# Check\n", "paper_doll('Hello')"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MMMiiissssssiiissssssiiippppppiii\n"]}], "source": ["# Check\n", "paper_doll('Mississippi')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### BLACKJACK: Given three integers between 1 and 11, if their sum is less than or equal to 21, return their sum. If their sum exceeds 21 *and* there's an eleven, reduce the total sum by 10. Finally, if the sum (even after adjustment) exceeds 21, return 'BUST'\n", "    blackjack(5,6,7) --> 18\n", "    blackjack(9,9,9) --> 'BUST'\n", "    blackjack(9,9,11) --> 19"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["def blackjack(a,b,c):\n", "    sum1 = a+b+c\n", "    if sum1 <= 21:\n", "        print(sum1)\n", "    elif a == 11 or b == 11 or c == 11:\n", "        if sum1 - 10 <= 21:\n", "            print(sum1-10)\n", "        else:\n", "            print(\"BUST\")\n", "    else:\n", "        print(\"BUST\")\n", "    pass"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18\n"]}], "source": ["# Check\n", "blackjack(5,6,7)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BUST\n"]}], "source": ["# Check\n", "blackjack(9,9,9)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19\n"]}], "source": ["# Check\n", "blackjack(9,9,11)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SUMMER OF '69: Return the sum of the numbers in the array, except ignore sections of numbers starting with a 6 and extending to the next 9 (every 6 will be followed by at least one 9). Return 0 for no numbers.\n", " \n", "    summer_69([1, 3, 5]) --> 9\n", "    summer_69([4, 5, 6, 7, 8, 9]) --> 9\n", "    summer_69([2, 1, 6, 9, 11]) --> 14"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["def summer_69(arr):\n", "    sum1 = 0\n", "    flag = 0\n", "    for i in arr:\n", "        if i == 6:\n", "            flag = 1\n", "        elif flag == 0:\n", "            sum1 += i\n", "        elif i == 9:\n", "            flag = 0\n", "    print(sum1)\n", "    pass"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n"]}], "source": ["# Check\n", "summer_69([1, 3, 5])"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n"]}], "source": ["# Check\n", "summer_69([4, 5, 6, 7, 8, 9])"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14\n"]}], "source": ["# Check\n", "summer_69([2, 1, 6, 9, 11])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CHALLENGING PROBLEMS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SPY GAME: Write a function that takes in a list of integers and returns True if it contains 007 in order\n", "\n", "     spy_game([1,2,4,0,0,7,5]) --> True\n", "     spy_game([1,0,2,4,0,5,7]) --> True\n", "     spy_game([1,7,2,0,4,5,0]) --> False\n"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [], "source": ["def spy_game(nums):\n", "    stri = \"\"\n", "    for i in nums:\n", "        stri += str(i)\n", "    if \"007\" in stri:\n", "        print(\"True\")\n", "    else:\n", "        print(\"False\")"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# Check\n", "spy_game([1,2,4,0,0,7,5])"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Check\n", "spy_game([1,0,2,4,0,5,7])"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["# Check\n", "spy_game([1,7,2,0,4,5,0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### COUNT PRIMES: Write a function that returns the *number* of prime numbers that exist up to and including a given number\n", "    count_primes(100) --> 25\n", "\n", "By convention, 0 and 1 are not prime."]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [], "source": ["from math import sqrt\n", "def is_prime(digit):\n", "    if digit <= 1:\n", "        return False\n", "    else:\n", "        for i in range(2,int(sqrt(digit)+1)):\n", "            if digit % i == 0:\n", "                return False\n", "        return True\n", "\n", "def count_primes(num):\n", "    sum_1 = 0\n", "    for i in range(num+1):\n", "        bol = is_prime(i)\n", "        if bol:\n", "            sum_1 += 1\n", "    print(sum_1)\n", "            \n", "                "]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["# Check\n", "count_primes(100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Just for fun:\n", "#### PRINT BIG: Write a function that takes in a single letter, and returns a 5x5 representation of that letter\n", "    print_big('a')\n", "    \n", "    out:   *  \n", "          * *\n", "         *****\n", "         *   *\n", "         *   *\n", "HINT: Consider making a dictionary of possible patterns, and mapping the alphabet to specific 5-line combinations of patterns. <br>For purposes of this exercise, it's ok if your dictionary stops at \"E\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["def print_big(letter):\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["print_big('a')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Great Job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}