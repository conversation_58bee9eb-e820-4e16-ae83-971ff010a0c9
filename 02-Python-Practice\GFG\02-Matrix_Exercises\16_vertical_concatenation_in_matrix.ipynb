{"cells": [{"cell_type": "markdown", "id": "629bf124-707e-4cc2-8ffe-4546eb041c54", "metadata": {}, "source": ["# Task: Vertical Concatenation in Matrix\n", "\n", "## Problem Statement:\n", "Given a string matrix (list of lists with strings), perform column-wise concatenation of strings, even when inner lists have varying lengths.\n", "\n", "### Steps:\n", "1. Determine the maximum number of columns present across all rows.\n", "2. Initialize an empty list to store concatenated strings for each column.\n", "3. Iterate through each column index.\n", "4. For each column index, iterate through all rows and collect existing elements.\n", "5. Concatenate collected strings and add to the result list.\n", "6. Return the final list with column-wise concatenated strings.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a6a34516-6640-415e-8d2c-e6ba74115b37", "metadata": {}, "outputs": [], "source": ["def vertical_concatenate_matrix(matrix):\n", "    res = []\n", "    N = 0\n", "    while N != len(matrix):\n", "        temp = ''\n", "        for idx in matrix:\n", "            try:\n", "                temp = temp + idx[N]\n", "            except IndexError:\n", "                pass\n", "        res.append(temp)\n", "        N += 1\n", "    res = [ele for ele in res if ele]\n", "    return res"]}, {"cell_type": "code", "execution_count": 3, "id": "fee98bf0-7583-4b21-bc4b-8f9f94e6fc3c", "metadata": {}, "outputs": [], "source": ["test_list = [[\"Gfg\", \"good\"], [\"is\", \"for\"], [\"Best\"]]"]}, {"cell_type": "code", "execution_count": 4, "id": "4265a991-207d-4783-8619-3829b5e2c70a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The original matrix: [['Gfg', 'good'], ['is', 'for'], ['Best']]\n"]}], "source": ["print(\"The original matrix:\", test_list)"]}, {"cell_type": "code", "execution_count": 5, "id": "db7075f7-52a9-42f0-83fd-1bd4e96d47f7", "metadata": {}, "outputs": [], "source": ["result = vertical_concatenate_matrix(test_list)"]}, {"cell_type": "code", "execution_count": 6, "id": "aa4bbd82-c132-41c2-81af-adaa20c02a5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matrix after vertical concatenation: ['GfgisBest', 'goodfor']\n"]}], "source": ["print(\"Matrix after vertical concatenation:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}