{"cells": [{"cell_type": "markdown", "id": "8e07a8e0-65a7-48e8-a7dd-dc43152d81bd", "metadata": {}, "source": ["# Task: Group Elements in Matrix\n", "\n", "## Problem Statement:\n", "Given a matrix with two columns, group the elements of the second column based on the first column.\n", "\n", "### Steps:\n", "1. Initialize the list of lists as the input matrix.\n", "2. Initialize an empty dictionary with default empty lists using dictionary comprehension. The keys will be the first elements of the tuples in the input matrix.\n", "3. Loop through the matrix and append the second element of each tuple to the list in the dictionary corresponding to the first column element.\n", "4. Return the resulting dictionary with the grouped elements.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5c88bf78-098e-4b2e-b331-4814e09ea848", "metadata": {}, "outputs": [], "source": ["test_list = [[5,8],[2,0],[5,4],[2,3],[7,9]]"]}, {"cell_type": "code", "execution_count": 2, "id": "2b804a31-0687-422c-8f5c-913a3afc009f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original: [[5, 8], [2, 0], [5, 4], [2, 3], [7, 9]]\n"]}], "source": ["print(\"Original: \"+str(test_list))"]}, {"cell_type": "code", "execution_count": 3, "id": "b7287aea-08aa-42c7-8262-d1ed7e69530e", "metadata": {}, "outputs": [], "source": ["res = {idx[0]: [] for idx in test_list}"]}, {"cell_type": "code", "execution_count": 4, "id": "45b0ee97-6420-4949-9db1-645e999e7a71", "metadata": {}, "outputs": [{"data": {"text/plain": ["{5: [], 2: [], 7: []}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 5, "id": "4157c713-d268-40fc-90fa-4a6ad3dbae2b", "metadata": {}, "outputs": [], "source": ["for idx in test_list:\n", "    res[idx[0]].append(idx[1])"]}, {"cell_type": "code", "execution_count": 6, "id": "4b10398d-9b83-49ed-87cc-71c1e7922796", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Grouped Matrix: {5: [8, 4], 2: [0, 3], 7: [9]}\n"]}], "source": ["print(\"The Grouped Matrix: \"+str(res))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}