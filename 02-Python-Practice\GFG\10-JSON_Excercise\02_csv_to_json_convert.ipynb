{"cells": [{"cell_type": "markdown", "id": "45c7b19b", "metadata": {}, "source": ["# Task: Convert CSV to JSON using Python\n", "\n", "## Problem Statement:\n", "Write a Python program to convert data from a CSV file into a JSON file. Each row of the CSV should be transformed into a dictionary and all such rows should be stored in a JSON array.\n", "\n", "## Steps:\n", "1. **Read the CSV file** using `pandas.read_csv()` or Python's built-in `csv` module.\n", "2. **Convert the DataFrame or rows to a list of dictionaries** using `.to_dict(orient='records')`.\n", "3. **Write the list of dictionaries to a JSON file** using `json.dump()`.\n"]}, {"cell_type": "markdown", "id": "e3cc9a06", "metadata": {}, "source": ["## Using csv and json modules"]}, {"cell_type": "code", "execution_count": 1, "id": "b64d87ff", "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "\n", "with open('CSV.csv', mode='r', newline='', encoding='utf-8') as csvfile:\n", "    data = list(csv.DictReader(csvfile))\n", "\n", "with open('output.json', mode='w', encoding='utf-8') as jsonfile:\n", "    json.dump(data, jsonfile, indent=4)"]}, {"cell_type": "markdown", "id": "14f79747", "metadata": {}, "source": ["## Using pandas library"]}, {"cell_type": "code", "execution_count": 2, "id": "4c3c1731", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv('CSV.csv')\n", "\n", "# DataFrame to JSON\n", "df.to_json('output.json', orient='records', lines=True)"]}, {"cell_type": "markdown", "id": "96e313ad", "metadata": {}, "source": ["## Using jsonlines"]}, {"cell_type": "code", "execution_count": 3, "id": "23218128", "metadata": {}, "outputs": [], "source": ["import csv\n", "import jsonlines\n", "\n", "with open('CSV.csv', mode='r', newline='', encoding='utf-8') as csvfile, jsonlines.open('output.jsonl', mode='w') as writer:\n", "    for row in csv.DictReader(csvfile):\n", "        writer.write(row)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}