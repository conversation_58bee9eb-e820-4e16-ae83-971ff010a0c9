{"cells": [{"cell_type": "markdown", "id": "0b2a3e92", "metadata": {}, "source": ["# Task: Cycle Sort Algorithm in Python\n", "\n", "## Problem Statement:\n", "Implement the **Cycle Sort** algorithm to sort a list of integers in-place. Cycle sort minimizes the number of memory writes, making it suitable when memory write operations are expensive.\n", "\n", "## Steps:\n", "1. **Start from the beginning** of the array and pick an element.\n", "2. **Count the number of elements smaller** than the current item to determine its correct position.\n", "3. If the item is not already in the correct position, **place it there** and displace the existing item.\n", "4. **Repeat the process** for the displaced item until the cycle is complete.\n", "5. Move to the next item and repeat the above steps until the array is sorted.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "811df4f3", "metadata": {}, "outputs": [], "source": ["def cycleSort(array):\n", "    writes = 0\n", "    for cycleStart in range(0, len(array) - 1):\n", "        item = array[cycleStart]\n", "        pos = cycleStart\n", "\n", "        for i in range(cycleStart + 1, len(array)):\n", "            if array[i] < item:\n", "                pos += 1\n", "        if pos == cycleStart:\n", "            continue\n", "        while item == array[pos]:\n", "            pos += 1\n", "        array[pos], item = item, array[pos]\n", "        writes += 1\n", "        while pos != cycleStart:\n", "            pos = cycleStart\n", "            for i in range(cycleStart + 1, len(array)):\n", "                if array[i] < item:\n", "                    pos += 1\n", "            while item == array[pos]:\n", "                pos += 1\n", "            array[pos], item = item, array[pos]\n", "            writes += 1\n", "    return writes"]}, {"cell_type": "code", "execution_count": 2, "id": "b8337b82", "metadata": {}, "outputs": [], "source": ["arr = [1, 8, 3, 9, 10, 10, 2, 4 ]"]}, {"cell_type": "code", "execution_count": 3, "id": "59609695", "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["n = len(arr)\n", "cycleSort(arr)"]}, {"cell_type": "code", "execution_count": 4, "id": "c31cf54a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After sort : \n", "1 2 3 4 8 9 10 10 "]}], "source": ["print(\"After sort : \")\n", "for i in range(0, n) :\n", "    print(arr[i], end = ' ')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}