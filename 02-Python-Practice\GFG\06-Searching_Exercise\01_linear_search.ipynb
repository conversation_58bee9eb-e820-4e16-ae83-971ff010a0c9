{"cells": [{"cell_type": "markdown", "id": "1878d4e6", "metadata": {}, "source": ["# Task: Linear Search - Python\n", "\n", "## Problem Statement:\n", "Given an array `arr` of `n` elements and an element `x`, write a Python program to check whether `x` is present in the array.  \n", "If it is found, return the index of its **first occurrence**. Otherwise, return **-1**.\n", "\n", "## Steps:\n", "\n", "1. **Take input** for the array and the element `x` to search.\n", "2. **Iterate** through each element of the array:\n", "   - If the current element is equal to `x`, return its index.\n", "3. If the element is not found after the iteration, return `-1`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4c6e28aa", "metadata": {}, "outputs": [], "source": ["def linear_search(arr, target):\n", "    for index in range(len(arr)):\n", "        if arr[index] == target:\n", "            return index\n", "    return -1"]}, {"cell_type": "code", "execution_count": 2, "id": "fa8fb453", "metadata": {}, "outputs": [], "source": ["arr = [10, 23, 45, 70, 11, 15]\n", "target = 70"]}, {"cell_type": "code", "execution_count": 3, "id": "7cbe0f68", "metadata": {}, "outputs": [], "source": ["result = linear_search(arr, target)"]}, {"cell_type": "code", "execution_count": 4, "id": "93275cde", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Element found at index: 3\n"]}], "source": ["if result != -1:\n", "    print(f\"Element found at index: {result}\")\n", "else:\n", "    print(\"Element not found in the array.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}