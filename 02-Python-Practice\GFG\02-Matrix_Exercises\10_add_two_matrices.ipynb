{"cells": [{"cell_type": "markdown", "id": "9d5c017a-2165-47cd-8380-20ac317efbac", "metadata": {}, "source": ["# Task: Add Two Matrices - Python\n", "\n", "## Problem Statement:\n", "Given two matrices of the same size, add them element-wise and return the resulting matrix.\n", "\n", "### Steps:\n", "1. Ensure both matrices are of the same dimensions.\n", "2. Iterate through each row and column using nested loops or list comprehension.\n", "3. Add the corresponding elements from both matrices.\n", "4. Construct and return the new matrix containing the summed values.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6d0d8284-1e80-471f-b212-19561d9ceccf", "metadata": {}, "outputs": [], "source": ["def add_matrices(mat1, mat2):\n", "    if len(mat1) != len(mat2) or len(mat1[0]) != len(mat2[0]):\n", "        raise ValueError(\"Matrices must have the same dimensions\")\n", "\n", "    result = [[mat1[i][j] + mat2[i][j] for j in range(len(mat1[0]))] for i in range(len(mat1))]\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 2, "id": "f3513639-ff5d-4faa-a558-a1d4214ac212", "metadata": {}, "outputs": [], "source": ["A = [[1,2],[3,4]]\n", "B = [[4,5],[6,7]]"]}, {"cell_type": "code", "execution_count": 3, "id": "abad0064-6beb-40c7-8fbe-e11b48339784", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[5, 7], [9, 11]]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["add_matrices(A,B)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}