{"cells": [{"cell_type": "markdown", "id": "e1d0b4c2", "metadata": {}, "source": ["# Task: Explore Python Library for Linked List\n", "\n", "## Problem Statement:\n", "Python’s built-in `list` is a dynamic array, not a true linked list. However, to work with actual **linked list structures**, we can use third-party libraries or implement our own.\n", "\n", "## Steps:\n", "\n", "1. **Understand the Limitation**:\n", "   - Python's built-in `list` is not a linked list (random access, not node-based).\n", "\n", "2. **Use `collections.deque` (for Doubly Linked List behavior)**:\n", "   - Supports fast appends and pops from both ends.\n", "   - Good for **queue** and **stack** implementations.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "53a733e7", "metadata": {}, "outputs": [], "source": ["from collections import deque"]}, {"cell_type": "code", "execution_count": 2, "id": "4a04ebbc", "metadata": {}, "outputs": [], "source": ["lnkd_lst = deque()"]}, {"cell_type": "code", "execution_count": 3, "id": "a7c3af05", "metadata": {}, "outputs": [], "source": ["lnkd_lst.append(4)\n", "lnkd_lst.append(5)\n", "lnkd_lst.append(6)\n", "lnkd_lst.append(7)"]}, {"cell_type": "code", "execution_count": 4, "id": "cd3f1254", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["elements in the linked_list:\n", "deque([4, 5, 6, 7])\n"]}], "source": ["print(\"elements in the linked_list:\")\n", "print(lnkd_lst)"]}, {"cell_type": "code", "execution_count": 5, "id": "279d395b", "metadata": {}, "outputs": [], "source": ["lnkd_lst.insert(1, 'fourth')"]}, {"cell_type": "code", "execution_count": 6, "id": "b6045106", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["deque([4, 'fourth', 5, 6, 7])\n"]}], "source": ["print(lnkd_lst)"]}, {"cell_type": "code", "execution_count": 7, "id": "93f1bd27", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["lnkd_lst.pop()"]}, {"cell_type": "code", "execution_count": 8, "id": "7571fe9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["deque([4, 'fourth', 5, 6])\n"]}], "source": ["print(lnkd_lst)"]}, {"cell_type": "code", "execution_count": 9, "id": "5eb91543", "metadata": {}, "outputs": [], "source": ["lnkd_lst.remove(\"fourth\")"]}, {"cell_type": "code", "execution_count": 10, "id": "cba5cf1a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["deque([4, 5, 6])\n"]}], "source": ["print(lnkd_lst)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}