{"cells": [{"cell_type": "markdown", "id": "313fe842", "metadata": {}, "source": ["# Task: Create an Enum Object and Show a Member Name and Value\n", "\n", "## Problem Statement:\n", "Write a Python program to define an `Enum` class, create members in it, and display both the **name** and **value** of a specific enum member.\n", "\n", "## Steps:\n", "1. Import the `Enum` class from the `enum` module.\n", "2. Define a new Enum class with a few named members.\n", "3. Access a member of the enum.\n", "4. Display the member’s name using `.name` and value using `.value`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8bf4b3f4", "metadata": {}, "outputs": [], "source": ["from enum import Enum"]}, {"cell_type": "code", "execution_count": 2, "id": "7c2e3ce8", "metadata": {}, "outputs": [], "source": ["class Country(Enum):\n", "    Albania = 355\n", "    Afghanistan = 93\n", "    Algeria = 213\n", "    Andorra = 376"]}, {"cell_type": "code", "execution_count": 3, "id": "8af883f2", "metadata": {}, "outputs": [], "source": ["member = Country.Albania"]}, {"cell_type": "code", "execution_count": 4, "id": "106117ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Member name: Albania\n", "Member value: 355\n"]}], "source": ["print(\"Member name:\", member.name)\n", "print(\"Member value:\", member.value)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}