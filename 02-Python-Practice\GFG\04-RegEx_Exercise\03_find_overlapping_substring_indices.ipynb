{"cells": [{"cell_type": "markdown", "id": "91649243-e1e0-4f37-a4d8-d60710a847b0", "metadata": {}, "source": ["# Task: Python Program to Find Indices of Overlapping Substrings\n", "\n", "## Problem Statement:\n", "Given a string and a substring, write a Python program to find all the starting indices where the substring appears in the string, including overlapping occurrences.\n", "\n", "## Steps:\n", "\n", "1. Import the `re` module (Regular Expressions).\n", "2. Define the target string and the substring you want to search.\n", "3. Use `re.finditer()` to find all matches of the substring in the string.\n", "4. To allow overlapping matches, use a lookahead pattern with the format `(?=...)`.\n", "   - Example: `(?=substring)`\n", "5. Use `re.escape()` on the substring to safely match any special characters.\n", "6. Iterate over the match objects returned by `finditer()`.\n", "7. Use `match.start()` to extract the starting index of each match.\n", "8. Store or print the indices.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fcb01814-ae91-49f4-8d12-bcee83c6e6fc", "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 2, "id": "57dca9a3-78b5-4d33-80de-d3af6eeaf777", "metadata": {}, "outputs": [], "source": ["def CntSubstr(pattern, string):\n", "    a = [m.start() for m in re.finditer(\n", "        '(?={0})'.format(re.escape(pattern)), string)]\n", "    return a"]}, {"cell_type": "code", "execution_count": 3, "id": "7144ab0e-e125-4838-99b6-97e2fe0457ec", "metadata": {}, "outputs": [], "source": ["string1 = 'geeksforgeeksforgeeks'\n", "pattern1 = 'geeksforgeeks'\n", "\n", "string2 = 'barfoobarfoobarfoobarfoobarfoo'\n", "pattern2 = 'foobarfoo'"]}, {"cell_type": "code", "execution_count": 4, "id": "d89a54ca-ac0d-46be-bef1-0b2e628c41da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 8]\n", "[3, 9, 15, 21]\n"]}], "source": ["print(CntSubstr(pattern1, string1))\n", "print(CntSubstr(pattern2, string2))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}