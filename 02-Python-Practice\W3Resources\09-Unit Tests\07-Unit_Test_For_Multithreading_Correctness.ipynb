{"cells": [{"cell_type": "markdown", "id": "a5945005", "metadata": {}, "source": ["# Task: Unit Test for Multi-Threading Correctness\n", "\n", "## Problem Statement:\n", "Write a Python **unit test** to verify whether a given function correctly handles **multi-threaded execution**, such as avoiding race conditions or maintaining expected shared state.\n", "\n", "## Steps:\n", "1. **Import** the `threading` and `unittest` modules.\n", "2. Define a **function** that performs operations on a shared resource.\n", "3. Use a **lock** (e.g., `threading.Lock()`) in the function to prevent race conditions.\n", "4. In the test case:\n", "   - Create **multiple threads** calling the shared function.\n", "   - **Start and join** all threads.\n", "   - **Assert** the final state of the shared resource to ensure correctness.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d6c56c3b", "metadata": {}, "outputs": [], "source": ["import unittest\n", "import threading"]}, {"cell_type": "code", "execution_count": 2, "id": "855c0a60", "metadata": {}, "outputs": [], "source": ["counter = 0\n", "lock = threading.Lock()"]}, {"cell_type": "code", "execution_count": 3, "id": "3a4835de", "metadata": {}, "outputs": [], "source": ["def increment_counter(times):\n", "    global counter\n", "    for _ in range(times):\n", "        with lock:\n", "            counter +=1"]}, {"cell_type": "code", "execution_count": 4, "id": "c6ff8797", "metadata": {}, "outputs": [], "source": ["class TestMultithreadingCorrectness(unittest.TestCase):\n", "    def test_thread_safe_incriment(self):\n", "        global counter\n", "        counter = 0\n", "\n", "        num_threads = 10\n", "        increments_per_thread = 1000\n", "\n", "        threads = []\n", "        for _ in range(num_threads):\n", "            thread = threading.Thread(target=increment_counter, args=(increments_per_thread,))\n", "            threads.append(thread)\n", "            thread.start()\n", "\n", "        for thread in threads:\n", "            thread.join()\n", "\n", "        expected_total = num_threads * increments_per_thread\n", "        self.assertEqual(counter, num_threads * increments_per_thread)"]}, {"cell_type": "code", "execution_count": 6, "id": "30d894c0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [".c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\session.py:203: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  return datetime.utcnow().replace(tzinfo=utc)\n", "\n", "----------------------------------------------------------------------\n", "Ran 1 test in 0.026s\n", "\n", "OK\n"]}], "source": ["if __name__ == '__main__':\n", "    unittest.main(argv=['first-arg-is-ignored'], exit=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}