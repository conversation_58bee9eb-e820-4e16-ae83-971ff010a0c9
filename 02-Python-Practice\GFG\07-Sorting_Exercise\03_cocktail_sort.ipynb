{"cells": [{"cell_type": "markdown", "id": "f1b416c0", "metadata": {}, "source": ["# Task: Python Program for Cocktail Sort\n", "\n", "## Problem Statement:\n", "Implement the **Cocktail Sort** algorithm, which is a variation of the Bubble Sort. It sorts the array by traversing in both directions in each pass—first left to right, then right to left—pushing largest and smallest elements to their correct positions.\n", "\n", "## Steps:\n", "1. Take the input array.\n", "2. Initialize `swapped` as `True` and set the `start` and `end` indices of the array.\n", "3. **While swapped is True**:\n", "   - Set `swapped` to `False`.\n", "   - Traverse the array from `start` to `end`, comparing adjacent elements.\n", "     - If the current element is greater than the next, swap them and set `swapped = True`.\n", "   - If `swapped` is still `False`, break (array is sorted).\n", "   - Decrease `end` by 1.\n", "   - Traverse the array from `end` to `start` in reverse.\n", "     - If the current element is smaller than the previous, swap them and set `swapped = True`.\n", "   - Increase `start` by 1.\n", "4. Continue until the array is fully sorted.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c5cd9bec", "metadata": {}, "outputs": [], "source": ["def cocktailSort(a):\n", "    n = len(a)\n", "    swapped = True\n", "    start = 0\n", "    end = n - 1\n", "    while swapped:\n", "        swapped = False\n", "        for i in range(start, end):\n", "            if a[i] > a[i + 1]:\n", "                a[i], a[i + 1] = a[i + 1], a[i]\n", "                swapped = True\n", "        if not swapped:\n", "            break\n", "        swapped = False\n", "        end -= 1\n", "        for i in range(end - 1, start - 1, -1):\n", "            if a[i] > a[i + 1]:\n", "                a[i], a[i + 1] = a[i + 1], a[i]\n", "                swapped = True\n", "        start += 1"]}, {"cell_type": "code", "execution_count": 2, "id": "d80e7705", "metadata": {}, "outputs": [], "source": ["a = [5, 1, 4, 2, 8, 0, 2]"]}, {"cell_type": "code", "execution_count": 3, "id": "e9ffd7b9", "metadata": {}, "outputs": [], "source": ["cocktailSort(a)"]}, {"cell_type": "code", "execution_count": null, "id": "68c23aae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sorted array is:\n", "0 1 2 2 4 5 8 "]}], "source": ["print(\"Sorted array is:\")\n", "for i in range(len(a)):\n", "    print(\"%d\" % a[i], end=\" \")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}