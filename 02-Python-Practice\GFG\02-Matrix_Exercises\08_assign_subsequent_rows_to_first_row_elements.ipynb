{"cells": [{"cell_type": "markdown", "id": "27cdfe18-dded-4570-8810-b58713c5a8c8", "metadata": {}, "source": ["# Task: Assigning Subsequent Rows to Matrix First Row Elements\n", "\n", "## Problem Statement:\n", "Given a (N + 1) * N matrix, assign each column of the first row of the matrix the subsequent row elements.\n", "\n", "### Steps:\n", "1. Initialize an empty dictionary where each key represents a column index and the value is a list.\n", "2. Use a loop to iterate over each column in the first row.\n", "3. For each column, use dictionary comprehension to assign values from the subsequent row to the first row columns.\n", "4. Replace the values in the first row with the corresponding values from the next row.\n", "5. Return the modified matrix with the updated first row.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a7d0a70b-8637-45b8-9b30-5857433f9c27", "metadata": {}, "outputs": [], "source": ["test_list = [[5, 8, 9], [2, 0, 9], [5, 4, 2], [2, 3, 9]]"]}, {"cell_type": "code", "execution_count": 2, "id": "26317ded-abb8-47db-bff7-23843e0ddba9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original: [[5, 8, 9], [2, 0, 9], [5, 4, 2], [2, 3, 9]]\n"]}], "source": ["print(\"Original: \"+str(test_list))"]}, {"cell_type": "code", "execution_count": 3, "id": "51d02fbc-2850-4334-88c2-f11181e6cfdf", "metadata": {}, "outputs": [], "source": ["res = {test_list[0][ele]: test_list[ele+1] for ele in range(len(test_list)-1)}"]}, {"cell_type": "code", "execution_count": 4, "id": "6ceb2783-ee77-4a77-b3c3-7509f93bb212", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Assigned Matrix : {5: [2, 0, 9], 8: [5, 4, 2], 9: [2, 3, 9]}\n"]}], "source": ["print(\"The Assigned Matrix : \" + str(res))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}