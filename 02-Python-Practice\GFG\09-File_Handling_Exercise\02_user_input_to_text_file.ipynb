{"cells": [{"cell_type": "markdown", "id": "4f6d89c1", "metadata": {}, "source": ["# Task: Take input from user and store in `.txt` file in Python\n", "\n", "## Problem Statement:\n", "The goal is to take input from the user through the console and save that input into a `.txt` file using Python. This process involves basic file handling and exception management using `try-except`.\n", "\n", "## Steps:\n", "1. Use the `input()` function to collect input from the user.\n", "2. Use the `open()` function with mode `'w'` or `'a'` to open a file for writing (create or append).\n", "3. Use the `.write()` method to save the input to the file.\n", "4. Use `try-except` to catch and handle any errors during the file operation.\n", "5. Optionally, use a `with` block to ensure the file is automatically closed after writing.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e849ba6c", "metadata": {}, "outputs": [], "source": ["temp = input(\"Enter the text you want to write in the file: \")"]}, {"cell_type": "code", "execution_count": 3, "id": "00e1ec1b", "metadata": {}, "outputs": [], "source": ["try:\n", "    with open(\"try.txt\", \"w\") as file:\n", "        file.write(temp)\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}