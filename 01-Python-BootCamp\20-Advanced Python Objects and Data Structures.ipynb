{"cells": [{"cell_type": "markdown", "id": "b17eab49-3119-4cbf-bbcc-1cd3068cb9fa", "metadata": {}, "source": ["# Module 20: Advanced Python Objects and Data Structures"]}, {"cell_type": "markdown", "id": "0e23f9a2-cd2d-47c9-a7b0-a1ea4b933703", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Advanced Numbers"]}, {"cell_type": "code", "execution_count": 1, "id": "ae2ee69a-b427-44c4-b2ec-299416c5797b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0xc'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["hex(12)"]}, {"cell_type": "code", "execution_count": 2, "id": "593a1e78-2cb8-4890-a011-896f82c0346f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0x200'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["hex(512)"]}, {"cell_type": "code", "execution_count": 3, "id": "54d38636-279c-4ec0-bd75-82f46a3ca8fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0b10011010010'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["bin(1234)"]}, {"cell_type": "code", "execution_count": 4, "id": "d2fe5a5c-5361-47d0-9b27-0aaff7b4ace0", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0b10000000'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["bin(128)"]}, {"cell_type": "code", "execution_count": 5, "id": "5aaa8d58-6e65-43c5-a546-4d81e5486da8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0b1000000000'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["bin(512)"]}, {"cell_type": "code", "execution_count": 6, "id": "d6fba468-4c4f-4930-9db1-2f6267171ecd", "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["2**4"]}, {"cell_type": "code", "execution_count": 7, "id": "3247c658-3a59-4884-8c42-6be9acee6997", "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pow(2,4)"]}, {"cell_type": "markdown", "id": "97831f7f-662a-4a03-b6cc-384b83c0979e", "metadata": {}, "source": ["**`pow()` Function in Python**\n", "\n", "The `pow()` function is used to calculate the power of a number.\n", "\n", "- **Syntax**:  \n", "  `pow(base, exp[, mod])`\n", "\n", "  - `base`: The number to raise.\n", "  - `exp`: The exponent.\n", "  - `mod` (optional): If provided, returns `(base ** exp) % mod`.\n", "\n", "- **Usage**:  \n", "  - Computes `base` raised to the power of `exp`.\n", "  - With the `mod` argument, performs efficient modular exponentiation.\n", "\n", "`pow()` is commonly used in calculations, cryptography, and algorithms requiring modular arithmetic.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "d3b54151-7bb1-493e-a875-0d15f9ee84a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pow(2,4,3)"]}, {"cell_type": "code", "execution_count": 9, "id": "25efc9b4-7fd8-4a6c-977a-12fb0a987c6d", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pow(2,3,4)"]}, {"cell_type": "code", "execution_count": 10, "id": "f8995e55-b0ef-45c8-a81c-e6ff8b946e7d", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["abs(-3)"]}, {"cell_type": "code", "execution_count": 11, "id": "3711285d-6bcb-41e0-a31e-3720ea06792d", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["abs(2)"]}, {"cell_type": "code", "execution_count": 12, "id": "10b8b190-bc2b-45ef-85f1-fe42cc2d0932", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["round(3.1)"]}, {"cell_type": "code", "execution_count": 13, "id": "56d3a623-15d0-4b65-8ca6-d6f170d00ca6", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["round(3.9)"]}, {"cell_type": "code", "execution_count": 14, "id": "9c11b6f6-8404-4b95-b43b-04fc408991b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["3.14"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["round(3.141592,2)"]}, {"cell_type": "markdown", "id": "268b396b-70d3-4f6e-a6fb-160f3c3c1400", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Advanced Strings"]}, {"cell_type": "code", "execution_count": 15, "id": "68645c6f-3518-4272-89c3-bcae2bfe6948", "metadata": {}, "outputs": [], "source": ["s = 'hello world'"]}, {"cell_type": "code", "execution_count": 16, "id": "9de5a5f3-5e07-4a93-b269-e91fd2a9eb17", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello world'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["s.capitalize()"]}, {"cell_type": "code", "execution_count": 17, "id": "4d2e8d9c-034a-44e0-b411-8f321ac3c0b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'HELLO WORLD'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["s.upper()"]}, {"cell_type": "code", "execution_count": 18, "id": "778eb9ae-1e0d-4c08-8fb1-d9b04fbdd693", "metadata": {}, "outputs": [{"data": {"text/plain": ["'hello world'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["s.lower()"]}, {"cell_type": "code", "execution_count": 19, "id": "82e6da6b-6766-404b-bfa4-58027829558c", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["s.count('o')"]}, {"cell_type": "code", "execution_count": 20, "id": "d7a376b0-60b9-4aec-b9f2-19842747508e", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["s.find('o')"]}, {"cell_type": "code", "execution_count": 21, "id": "bd820c3d-6a2c-4e25-b77d-1346f37fba42", "metadata": {}, "outputs": [{"data": {"text/plain": ["'zzzzhello worldzzzzz'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["s.center(20,'z')"]}, {"cell_type": "code", "execution_count": 22, "id": "9c20caf2-90d0-4350-bce1-6f420b799e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["'hello   hi'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["'hello\\thi'.expandtabs()"]}, {"cell_type": "code", "execution_count": 23, "id": "1a2a8686-a5a3-45d4-965f-f97310440ddf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello\thi\n"]}], "source": ["print('hello\\thi')"]}, {"cell_type": "code", "execution_count": 24, "id": "31bfdbf1-c317-4c34-b045-fd7b0680539c", "metadata": {}, "outputs": [], "source": ["s = 'hello'"]}, {"cell_type": "code", "execution_count": 25, "id": "0cb19128-a59b-40da-b818-13ba57e45dfb", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["s.isalnum()"]}, {"cell_type": "code", "execution_count": 26, "id": "0746a7dd-8a07-4ee1-bbb4-a28f378ca885", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["s.isalpha()"]}, {"cell_type": "code", "execution_count": 27, "id": "8433594d-9a0e-4211-aca8-7ed9513302c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["s.islower()"]}, {"cell_type": "code", "execution_count": 28, "id": "173e7b0e-e27b-4c46-a9c5-9c1d7c34b4dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["s.isspace()"]}, {"cell_type": "code", "execution_count": 29, "id": "db214bae-f5a0-4f50-b3b9-0dbc6e6fdd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": [" s.istitle()"]}, {"cell_type": "code", "execution_count": 30, "id": "d1d9efc3-48ad-45fd-ae14-82cfc4a4191f", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["'Hello'.istitle()"]}, {"cell_type": "code", "execution_count": 31, "id": "e79eccb7-33ea-4e84-969b-56edb2b19122", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["s.isupper()"]}, {"cell_type": "code", "execution_count": 32, "id": "01fe069c-4226-4755-91a9-104a9797c70b", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["'HELLO'.isupper()"]}, {"cell_type": "code", "execution_count": 33, "id": "934ed52d-9f65-4fdd-a1b1-baec022469c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["s.endswith('o')"]}, {"cell_type": "code", "execution_count": 34, "id": "aaab5f67-67b9-4c4d-9dae-b49d583c6d63", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["s[-1] == 'o'"]}, {"cell_type": "code", "execution_count": 35, "id": "f81ad2ca-488e-4d9b-90c8-e94d79e1cc7d", "metadata": {}, "outputs": [{"data": {"text/plain": ["['h', 'llo']"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["s.split('e')"]}, {"cell_type": "code", "execution_count": 36, "id": "d4240e60-f2c3-4ce9-940f-c9d57372b6e2", "metadata": {}, "outputs": [], "source": ["s = 'hiihhihihihhhi'"]}, {"cell_type": "code", "execution_count": 37, "id": "d3709555-596d-42a8-bc39-d1405633de48", "metadata": {}, "outputs": [{"data": {"text/plain": ["['h', '', 'hh', 'h', 'h', 'hhh', '']"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["s.split('i')"]}, {"cell_type": "code", "execution_count": 38, "id": "d9022d3d-3511-4dd5-a320-0311ed6fcf31", "metadata": {}, "outputs": [{"data": {"text/plain": ["('h', 'i', 'ihhihihihhhi')"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["s.partition('i')"]}, {"cell_type": "markdown", "id": "42b4118d-e1fb-462f-b8e7-7a5070c91749", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Advanced Sets"]}, {"cell_type": "code", "execution_count": 39, "id": "6c3666c6-7943-460b-9ba9-502a91c21ae8", "metadata": {}, "outputs": [], "source": ["s = set()"]}, {"cell_type": "code", "execution_count": 40, "id": "866c6e5f-ffc2-4e4a-8673-d5a0f7850792", "metadata": {}, "outputs": [], "source": ["s.add(1)"]}, {"cell_type": "code", "execution_count": 41, "id": "fa4ca682-ceb0-44a6-bdac-0c96e6696161", "metadata": {}, "outputs": [], "source": ["s.add(2)"]}, {"cell_type": "code", "execution_count": 42, "id": "662ba0b3-51a3-4659-bec3-66a44139e080", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 43, "id": "fde65b72-fef4-4321-94dc-ac83051ac54b", "metadata": {}, "outputs": [], "source": ["s.clear()"]}, {"cell_type": "code", "execution_count": 44, "id": "5497c500-41de-4200-b25d-7698d5f75c49", "metadata": {}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 45, "id": "3aad5939-3c6c-43b4-8e37-c5a612cf0a56", "metadata": {}, "outputs": [], "source": ["s = {1,2,3}"]}, {"cell_type": "code", "execution_count": 46, "id": "ee11a002-cc57-42de-b4fb-632ff2029ad2", "metadata": {}, "outputs": [], "source": ["sc = s.copy()"]}, {"cell_type": "code", "execution_count": 47, "id": "6475c5df-88ee-464c-8558-fc4320f19e2f", "metadata": {}, "outputs": [], "source": ["s.add(4)"]}, {"cell_type": "code", "execution_count": 48, "id": "e71f2d1d-8390-467a-b29d-eb36dff01a4f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3, 4}"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 49, "id": "acda58af-38dc-4e3d-8774-088e85692fa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["sc"]}, {"cell_type": "code", "execution_count": 50, "id": "fbe6043a-5e01-45e3-8430-5e4072ef7ea6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{4}"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["s.difference(sc)"]}, {"cell_type": "code", "execution_count": 51, "id": "5ab13ac3-8992-4b63-96b0-b9a546268de3", "metadata": {}, "outputs": [], "source": ["s1 = {1,2,3}"]}, {"cell_type": "code", "execution_count": 52, "id": "16101024-01e1-40e1-86eb-c10e1739026e", "metadata": {}, "outputs": [], "source": ["s2 = {1,4,5}"]}, {"cell_type": "code", "execution_count": 53, "id": "1d7c0322-322f-4dd3-9232-f8447a33210f", "metadata": {}, "outputs": [], "source": ["s1.difference_update(s2)"]}, {"cell_type": "code", "execution_count": 54, "id": "c084dceb-46da-4ccf-ae3c-1e91954d6028", "metadata": {}, "outputs": [{"data": {"text/plain": ["{2, 3}"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["s1"]}, {"cell_type": "code", "execution_count": 55, "id": "0a942dd6-718f-40ca-80d3-4649341d9f9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 4, 5}"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["s2"]}, {"cell_type": "code", "execution_count": 56, "id": "88ef323e-249a-4fa1-a6e2-f4e28a191359", "metadata": {}, "outputs": [], "source": ["s.discard(2)"]}, {"cell_type": "code", "execution_count": 57, "id": "fc601d2b-9014-4284-ac3a-e99b155ccc58", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 3, 4}"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 58, "id": "32d994ef-0e54-4b0f-9219-400a482c51bb", "metadata": {}, "outputs": [], "source": ["s.discard(12)  # If the element is not present in the sent nothing happens"]}, {"cell_type": "code", "execution_count": 59, "id": "b1447bda-fdfd-4a64-b413-61ecae57f696", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 3, 4}"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 60, "id": "41bcb697-33c6-415b-9930-550e632df2ee", "metadata": {}, "outputs": [], "source": ["s1 = {1,2,3}"]}, {"cell_type": "code", "execution_count": 61, "id": "5ffb0ecb-f264-456a-b117-1449e4200fef", "metadata": {}, "outputs": [], "source": ["s2 = {1,2,4}"]}, {"cell_type": "code", "execution_count": 62, "id": "e41f03de-95b6-4575-aed1-15e5d78eaeb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2}"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.intersection(s2)"]}, {"cell_type": "code", "execution_count": 63, "id": "5515c4da-e77d-4562-b7ae-2e5724007210", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3}"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["s1"]}, {"cell_type": "code", "execution_count": 64, "id": "1c8548b9-eb89-44d5-9bf7-938d30a337ba", "metadata": {}, "outputs": [], "source": ["s1.intersection_update(s2)"]}, {"cell_type": "code", "execution_count": 65, "id": "d6f1f016-1267-47e3-85c0-97cf0ed0a535", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2}"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["s1"]}, {"cell_type": "code", "execution_count": 66, "id": "fad37aa4-5152-45d3-ba73-977d1d55f501", "metadata": {}, "outputs": [], "source": ["s1 = {1,2}\n", "s2 = {1,2,4}\n", "s3 = {5}"]}, {"cell_type": "code", "execution_count": 67, "id": "db45a06f-2c5d-47b9-af7b-a77a165112e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.isdisjoint(s2)"]}, {"cell_type": "code", "execution_count": 68, "id": "2cca74df-8b08-4660-a1d7-00b1ba05c528", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.isdisjoint(s3)"]}, {"cell_type": "code", "execution_count": 69, "id": "f91a4894-a485-4e2b-957e-65130f235e32", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.issubset(s2)"]}, {"cell_type": "code", "execution_count": 70, "id": "55153369-86e8-4295-bdb5-73d20a0f8e2d", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["s2.issuperset(s1)"]}, {"cell_type": "code", "execution_count": 71, "id": "6fdb1ae3-2d2e-43f1-8b6e-80600d5b4e5f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{4}"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.symmetric_difference(s2)"]}, {"cell_type": "code", "execution_count": 72, "id": "7e06bc5e-446b-4f34-a3e4-90fcbcb31c23", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 4}"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["s1.union(s2)"]}, {"cell_type": "code", "execution_count": 73, "id": "787f8697-d8c5-41fb-baf7-3f87b5022581", "metadata": {}, "outputs": [], "source": ["s1.update(s2)"]}, {"cell_type": "code", "execution_count": 74, "id": "9a1e5d4f-cb79-4409-a2e7-c18955e44122", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 4}"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["s1"]}, {"cell_type": "markdown", "id": "f568db09-581a-4126-9919-039277a0ea94", "metadata": {}, "source": ["## Advanced Dictionaries"]}, {"cell_type": "code", "execution_count": 75, "id": "1de7485a-a7c6-4f4d-9648-ec87b27d661e", "metadata": {}, "outputs": [], "source": ["d = {'k1':1,'k2':2}"]}, {"cell_type": "code", "execution_count": 76, "id": "3f1294a6-eb45-47bc-9ede-dd91dedf2203", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 0, 1: 1, 2: 4, 3: 9, 4: 16, 5: 25, 6: 36, 7: 49, 8: 64, 9: 81}"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["{x:x**2 for x in range(10)}"]}, {"cell_type": "code", "execution_count": 77, "id": "9eda22e8-1114-422d-b66f-615f5cbd0808", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a': 0, 'b': 1}"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["{k:v**2 for k,v in zip(['a','b'],range(2))}"]}, {"cell_type": "code", "execution_count": 78, "id": "95dd95f4-e0b6-4ed4-ab7b-fb57c4beaacf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n"]}], "source": ["for k in d.values():\n", "    print(k)"]}, {"cell_type": "code", "execution_count": 79, "id": "43bc8cb4-a650-49d4-a446-23f40f9c11af", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_values([1, 2])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["d.values()"]}, {"cell_type": "code", "execution_count": 80, "id": "3b32abc6-ce12-441d-a3c8-9716e55d8d00", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['k1', 'k2'])"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["d.keys()"]}, {"cell_type": "markdown", "id": "c8db43f9-0838-47a7-9c96-65e6ebbebdce", "metadata": {}, "source": ["## Advanced Lists"]}, {"cell_type": "code", "execution_count": 81, "id": "2f01ed4d-1339-4e10-8f15-144181773f61", "metadata": {}, "outputs": [], "source": ["l = [1,2,3]"]}, {"cell_type": "code", "execution_count": 82, "id": "4ff4acb3-c61d-4cad-be88-2a995a39fd0f", "metadata": {}, "outputs": [], "source": ["l.append(4)"]}, {"cell_type": "code", "execution_count": 83, "id": "d6d58d15-b3c9-4f47-a09c-799c37d852ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 84, "id": "5d90413d-09a6-4121-8391-9bb046a9d183", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["l.count(1)"]}, {"cell_type": "code", "execution_count": 85, "id": "503bbbfa-8d94-4168-b7b6-0e6ce911fd76", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["l.count(10)"]}, {"cell_type": "code", "execution_count": 86, "id": "7e90325f-30f9-49a1-9dfc-2de1e746da1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, [4, 5]]"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1,2,3]\n", "x.append([4,5])\n", "x"]}, {"cell_type": "code", "execution_count": 87, "id": "9d8708b6-8657-4618-be2e-67b06816867c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5]"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1,2,3]\n", "x.extend([4,5])\n", "x"]}, {"cell_type": "code", "execution_count": 88, "id": "82dc49ae-df58-4bbd-9d1a-130284f4c62f", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["l.index(2)"]}, {"cell_type": "code", "execution_count": 89, "id": "696c8a25-f1c8-4aae-9bd4-57565b896b77", "metadata": {}, "outputs": [], "source": ["# l.index(5) #Gives an error as 5 is not inthe list"]}, {"cell_type": "code", "execution_count": 90, "id": "97d69a05-6af3-494b-9c54-9c6d4e9d5464", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 91, "id": "43315f4b-d245-468b-a607-02dd8cc62028", "metadata": {}, "outputs": [], "source": ["l.insert(2,'inserted')"]}, {"cell_type": "code", "execution_count": 92, "id": "09be11e5-8fb3-456a-ae10-438cd0398c94", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 'inserted', 3, 4]"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 93, "id": "8782aa47-fb0e-4d2e-94aa-7f5b9fb3a34d", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["l.pop()"]}, {"cell_type": "code", "execution_count": 94, "id": "bda5d1f0-b830-43cf-9a17-8ad5ca48f7cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 'inserted', 3]"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 95, "id": "d9a88a29-cef8-49d9-9143-e557ccd7f4ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["'inserted'"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["l.pop(2)"]}, {"cell_type": "code", "execution_count": 96, "id": "df7cded3-cf02-43c9-82c5-9a6d5dce49e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 97, "id": "59a2f1ec-0034-4a0d-94f4-d2a7aa9a8fce", "metadata": {}, "outputs": [], "source": ["l.remove(3) # It will remove first occurence of the value"]}, {"cell_type": "code", "execution_count": 98, "id": "cb9a2077-f8d0-4170-9539-4c9acdc9ecb1", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 99, "id": "28563723-71eb-4827-8b6d-86bf9ca16d71", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["l = [1,3,2,3]\n", "l.remove(3)\n", "l"]}, {"cell_type": "code", "execution_count": 100, "id": "ef00bf8a-b474-42db-b2be-c357f37b6ce4", "metadata": {}, "outputs": [], "source": ["l.reverse()"]}, {"cell_type": "code", "execution_count": 101, "id": "dc13c487-ddfb-4d9e-8b23-1abb186897ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["[3, 2, 1]"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}, {"cell_type": "code", "execution_count": 102, "id": "782696ce-c01c-48af-a09a-1b0d6a7a288a", "metadata": {}, "outputs": [], "source": ["l.sort()"]}, {"cell_type": "code", "execution_count": 103, "id": "594de45b-d2d0-4c60-8bf1-34bf23371b0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["l"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}