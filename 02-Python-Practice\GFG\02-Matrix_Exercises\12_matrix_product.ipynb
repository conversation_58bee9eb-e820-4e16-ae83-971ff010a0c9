{"cells": [{"cell_type": "markdown", "id": "6706e32f-1ef8-42b1-bbe8-07028ca3e49e", "metadata": {}, "source": ["# Task: Matrix Product - Python\n", "\n", "## Problem Statement:\n", "Given a matrix (2D list), the task is to calculate the product of all its elements and return the final cumulative result.\n", "\n", "### Steps:\n", "1. Initialize a variable to hold the product (starting with 1).\n", "2. Iterate through each row of the matrix.\n", "3. For each row, iterate through its elements and multiply them into the product variable.\n", "4. Return the final product after processing all elements.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "55526cc1-48d2-4343-b266-63c9677b76be", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "a0d9a3b9-52a1-48c6-8ea1-66ca3e61afac", "metadata": {}, "outputs": [], "source": ["def mat_prod(matrix):\n", "    np_matrix = np.array(matrix)\n", "\n", "    result = np.prod(np_matrix)\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 6, "id": "980c665c-8909-456a-bfd5-980abd001f41", "metadata": {}, "outputs": [], "source": ["matrix = [\n", "    [1, 2, 3],\n", "    [4, 5, 6],\n", "    [7, 8, 9]\n", "]"]}, {"cell_type": "code", "execution_count": 7, "id": "3e56d712-25ce-4e36-9dd5-37d44efcb933", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(362880)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["mat_prod(matrix)"]}, {"cell_type": "code", "execution_count": 8, "id": "5d74aa60-3a22-481d-83cd-60b86d15b73f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1622880\n"]}], "source": ["import numpy as np\n", "a = [[1, 4, 5], [7, 3], [4], [46, 7, 3]]\n", "\n", "b = [ele for sub in a for ele in sub]  # Flattening\n", "res = np.prod(b)\n", "\n", "print(res)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}