{"cells": [{"cell_type": "markdown", "id": "4542b8e6", "metadata": {}, "source": ["# Task: Age Calculation using Pendulum\n", "\n", "## Problem Statement:\n", "Write a Python program to **calculate a person’s age** from their **birthdate** using the `pendulum` module, which provides enhanced date and time handling.\n", "\n", "## Steps:\n", "1. **Import** the `pendulum` module.\n", "2. **Take the birthdate** as input or define it using `pendulum.datetime(year, month, day)`.\n", "3. Get the **current date** using `pendulum.now()`.\n", "4. Use the `diff()` method to find the **difference in years** between the current date and the birthdate.\n", "5. **Print the age** in years.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5211333e", "metadata": {}, "outputs": [], "source": ["import pendulum"]}, {"cell_type": "code", "execution_count": 2, "id": "b216163a", "metadata": {}, "outputs": [], "source": ["def calculate_age(birth_date):\n", "    current_date = pendulum.now()\n", "    birthdate = pendulum.parse(birth_date)\n", "    age = current_date.diff(birthdate).in_years()\n", "    return age"]}, {"cell_type": "code", "execution_count": 3, "id": "62d4593d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The person's age with birthdate 1998-01-01 is 27 years.\n"]}], "source": ["birthdate_str = \"1998-01-01\"\n", "person_age = calculate_age(birthdate_str)\n", "print(f\"The person's age with birthdate {birthdate_str} is {person_age} years.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}