{"cells": [{"cell_type": "markdown", "id": "aa58e7b8", "metadata": {}, "source": ["# Module 6"]}, {"cell_type": "markdown", "id": "c4bbebff", "metadata": {}, "source": ["## Methods in Python"]}, {"cell_type": "code", "execution_count": 3, "id": "18564af8", "metadata": {}, "outputs": [], "source": ["mylist = [1,2,3,4,5]"]}, {"cell_type": "code", "execution_count": 4, "id": "c1b7e00b", "metadata": {}, "outputs": [], "source": ["mylist.append(6)"]}, {"cell_type": "code", "execution_count": 5, "id": "29efa14c", "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist.pop()"]}, {"cell_type": "markdown", "id": "bff6acd9", "metadata": {}, "source": ["**Methods are functions that are associated with objects. They are called on objects and can either modify the object itself or return a new object. For example, append() is a method that adds an element to a list, while pop() is a method that removes and returns the last element from a list. There are many such methods available for different types of objects. You can refer to the official documentation to explore all available methods for a particular object type.**"]}, {"cell_type": "markdown", "id": "dad9944f", "metadata": {}, "source": ["## Functions in Python"]}, {"cell_type": "code", "execution_count": 7, "id": "3cd33185", "metadata": {}, "outputs": [], "source": ["def say_hello():\n", "    print(\"Hello\")"]}, {"cell_type": "code", "execution_count": 8, "id": "412e1c8a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello\n"]}], "source": ["say_hello()"]}, {"cell_type": "code", "execution_count": 9, "id": "c8114ac0", "metadata": {}, "outputs": [], "source": ["def say_hello(name):\n", "    print(f\"Hello, {name}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "8e2a269c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, MR\n"]}], "source": ["say_hello(\"MR\")"]}, {"cell_type": "code", "execution_count": 12, "id": "0ddb9df2", "metadata": {}, "outputs": [], "source": ["def add_num(a,b):\n", "    return a + b"]}, {"cell_type": "markdown", "id": "080e0c22", "metadata": {}, "source": ["**return statements are used to return a value from a function. It allows you to save value to a Variable.**"]}, {"cell_type": "code", "execution_count": 13, "id": "06e1d235", "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["add_num(5, 10)"]}, {"cell_type": "code", "execution_count": 14, "id": "46450c72", "metadata": {}, "outputs": [], "source": ["ans = add_num(5, 10)"]}, {"cell_type": "code", "execution_count": 19, "id": "0caae820", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["def even_check(num):\n", "    result = num % 2 == 0\n", "    return result\n", "\n", "even_check(10)"]}, {"cell_type": "code", "execution_count": null, "id": "30b2336a", "metadata": {}, "outputs": [], "source": ["# Return True if any number in the list is even\\\\\n", "\n", "def check_even_in_list(num_list):\n", "    for num in num_list:\n", "        if even_check(num):\n", "            return True\n", "        else:\n", "            pass  # We can't return <PERSON>als<PERSON> here, because we want to check all numbers in the list\n", "    return False"]}, {"cell_type": "code", "execution_count": 30, "id": "76b5c3ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["check_even_in_list([1, 3, 5])"]}, {"cell_type": "code", "execution_count": 31, "id": "6f046b8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["check_even_in_list([1, 3, 5, 7, 8])"]}, {"cell_type": "markdown", "id": "6ab4f37e", "metadata": {}, "source": ["## <PERSON><PERSON> unpacking"]}, {"cell_type": "code", "execution_count": 32, "id": "d4a23907", "metadata": {}, "outputs": [], "source": ["work_hours = [('<PERSON>',100),('<PERSON>',400),('<PERSON>',800)]"]}, {"cell_type": "code", "execution_count": 33, "id": "c2697f3c", "metadata": {}, "outputs": [], "source": ["def employee_check(work_hours):\n", "    current_max = 0\n", "    employee_of_month = ''\n", "\n", "    for employee, hours in work_hours:\n", "        if hours > current_max:\n", "            current_max = hours\n", "            employee_of_month = employee\n", "        else:\n", "            pass\n", "    \n", "    return (employee_of_month,current_max)"]}, {"cell_type": "code", "execution_count": 34, "id": "fd165420", "metadata": {}, "outputs": [{"data": {"text/plain": ["('<PERSON>', 800)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["employee_check(work_hours)"]}, {"cell_type": "code", "execution_count": 35, "id": "27c0353a", "metadata": {}, "outputs": [], "source": ["work_hours.append(('david', 1000))"]}, {"cell_type": "code", "execution_count": 36, "id": "3535c06e", "metadata": {}, "outputs": [{"data": {"text/plain": ["('david', 1000)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["employee_check(work_hours)"]}, {"cell_type": "code", "execution_count": 37, "id": "70a9d473", "metadata": {}, "outputs": [], "source": ["name, hours = employee_check(work_hours)"]}, {"cell_type": "code", "execution_count": 38, "id": "8a13f7cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Employee of the month is david with 1000 hours worked.\n"]}], "source": ["print(f'Employee of the month is {name} with {hours} hours worked.')"]}, {"cell_type": "markdown", "id": "0b9246b5", "metadata": {}, "source": ["## Interaction between Python functions"]}, {"cell_type": "code", "execution_count": 39, "id": "8998e121", "metadata": {}, "outputs": [], "source": ["example = [1,2,3,4,5]"]}, {"cell_type": "code", "execution_count": 41, "id": "41d8563c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[3, 2, 4, 1, 5]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["from random import shuffle\n", "\n", "shuffle(example)\n", "example"]}, {"cell_type": "code", "execution_count": 42, "id": "5f6802aa", "metadata": {}, "outputs": [], "source": ["def shuffle_list(mylist):\n", "    shuffle(mylist)\n", "    return mylist"]}, {"cell_type": "code", "execution_count": 43, "id": "ff1ca783", "metadata": {}, "outputs": [], "source": ["mylist = [' ','O',' ']"]}, {"cell_type": "code", "execution_count": 45, "id": "34996606", "metadata": {}, "outputs": [{"data": {"text/plain": ["['O', ' ', ' ']"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["shuffle_list(mylist)"]}, {"cell_type": "code", "execution_count": 48, "id": "2cf4a6a7", "metadata": {}, "outputs": [], "source": ["def player_guess():\n", "    guess = ''\n", "    \n", "    while guess not in ['0', '1', '2']:\n", "        guess = input('Pick a number 0, 1, or 2: ')\n", "    return int(guess)"]}, {"cell_type": "code", "execution_count": 51, "id": "695f29a6", "metadata": {}, "outputs": [], "source": ["myindex = player_guess()"]}, {"cell_type": "code", "execution_count": 52, "id": "41e9ae70", "metadata": {}, "outputs": [], "source": ["def check_guess(mylist, guess):\n", "    if mylist[guess] == 'O':\n", "        print(\"Correct!\")\n", "    else:\n", "        print(\"Wrong guess!\")\n", "        print(mylist)"]}, {"cell_type": "code", "execution_count": 60, "id": "5e0052d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correct!\n"]}], "source": ["#Initial list\n", "mylist = [' ','O',' ']\n", "\n", "# Shuffle the list\n", "shuffle_list(mylist)\n", "\n", "# Player guess\n", "myindex = player_guess()\n", "\n", "# Check the guess\n", "check_guess(mylist, myindex)"]}, {"cell_type": "markdown", "id": "08a67742", "metadata": {}, "source": ["## *args and **kwargs"]}, {"cell_type": "markdown", "id": "632c8ddb", "metadata": {}, "source": ["**`*args` and `**kwargs` in Python**\n", "\n", "In Python, `*args` and `**kwargs` are used to pass a variable number of arguments to a function.\n", "\n", "- **`*args`**: Allows passing a variable number of positional arguments. These are stored as a tuple.\n", "- **`**kwargs`**: Allows passing a variable number of keyword arguments. These are stored as a dictionary.\n", "\n", "Use `*args` when you don’t know how many positional arguments will be passed and `**kwargs` for keyword arguments where you pass values using names.\n"]}, {"cell_type": "code", "execution_count": 61, "id": "2edfe719", "metadata": {}, "outputs": [], "source": ["def myfunc(a,b):\n", "    return sum((a,b)) * 0.05"]}, {"cell_type": "code", "execution_count": 62, "id": "9e0fc452", "metadata": {}, "outputs": [{"data": {"text/plain": ["5.0"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["my<PERSON><PERSON>(40,60)"]}, {"cell_type": "code", "execution_count": 63, "id": "cdbdd8fe", "metadata": {}, "outputs": [], "source": ["def myfunc(*args):\n", "    return sum(args) * 0.05"]}, {"cell_type": "code", "execution_count": 65, "id": "2dd4ca3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["10.0"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["myfunc(40,60,100)"]}, {"cell_type": "code", "execution_count": 68, "id": "40c28337", "metadata": {}, "outputs": [], "source": ["def myfunc(**kwargs):\n", "    if 'fruit' in kwargs:\n", "        print(f\"My fruit of choice is {kwargs['fruit']}\")\n", "    else:\n", "        print(\"I did not find any fruit here\")"]}, {"cell_type": "code", "execution_count": 69, "id": "d1e626f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["My fruit of choice is apple\n"]}], "source": ["myfunc(fruit='apple', veggie='lettuce')"]}, {"cell_type": "code", "execution_count": 70, "id": "615ad812", "metadata": {}, "outputs": [], "source": ["def myfunc(*args,**kwargs):\n", "    print(\"I would like {} {}\".format(args[0],kwargs['food']))"]}, {"cell_type": "code", "execution_count": 72, "id": "86cd84e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I would like 10 pizza\n"]}], "source": ["myfunc(10,20,30,fruit='apple', food='pizza', animal='dog')"]}, {"cell_type": "markdown", "id": "5d800d6c", "metadata": {}, "source": ["## Maps, Filters and Lamba Functions"]}, {"cell_type": "markdown", "id": "577539dc", "metadata": {}, "source": ["**`map()` in Python**\n", "\n", "The `map()` function in Python applies a given function to all items in an iterable (such as a list or tuple) and returns a map object (iterator).\n", "\n", "- **Syntax**: `map(function, iterable)`\n", "- **function**: The function to apply to each item.\n", "- **iterable**: The iterable whose elements are processed by the function.\n", "\n", "`map()` is useful when you want to transform or modify each element of an iterable without writing an explicit loop.\n"]}, {"cell_type": "code", "execution_count": 76, "id": "aa5d6c9f", "metadata": {}, "outputs": [], "source": ["def square(num):\n", "    return num ** 2"]}, {"cell_type": "code", "execution_count": 77, "id": "b1f15419", "metadata": {}, "outputs": [], "source": ["my_nums = [1,2,3,4,5]"]}, {"cell_type": "code", "execution_count": 78, "id": "b11336cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "4\n", "9\n", "16\n", "25\n"]}], "source": ["for item in map(square, my_nums):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 79, "id": "061bbad0", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(square, my_nums))"]}, {"cell_type": "code", "execution_count": 80, "id": "f4a72c44", "metadata": {}, "outputs": [], "source": ["def splicer(mystring):\n", "    if len(mystring) % 2 == 0:\n", "        return 'EVEN'\n", "    else:\n", "        return mystring[0]"]}, {"cell_type": "code", "execution_count": 81, "id": "2f5b47a5", "metadata": {}, "outputs": [{"data": {"text/plain": ["['EVEN', 'E', 'S']"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["names = ['<PERSON>', '<PERSON>', '<PERSON>']\n", "\n", "list(map(splicer, names))"]}, {"cell_type": "markdown", "id": "7495ed57", "metadata": {}, "source": ["**`filter()` in Python**\n", "\n", "The `filter()` function is used to filter elements from an iterable based on a condition defined in a function. It returns an iterator that contains only the elements for which the function returns `True`.\n", "\n", "- **Syntax**: `filter(function, iterable)`\n", "- **function**: A function that returns `True` or `False` for each element.\n", "- **iterable**: The iterable to be filtered.\n", "\n", "`filter()` is useful when you want to keep only those elements from an iterable that meet a specific condition.\n"]}, {"cell_type": "code", "execution_count": 82, "id": "28cc656b", "metadata": {}, "outputs": [], "source": ["def check_even(num):\n", "    return num % 2 == 0"]}, {"cell_type": "code", "execution_count": 83, "id": "02ba527b", "metadata": {}, "outputs": [], "source": ["mynums = [1,2,3,4,5,6,7,8,9,10]"]}, {"cell_type": "code", "execution_count": 84, "id": "a8c5c992", "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 4, 6, 8, 10]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["list(filter(check_even,mynums))"]}, {"cell_type": "code", "execution_count": 86, "id": "d294bf2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "4\n", "6\n", "8\n", "10\n"]}], "source": ["for n in filter(check_even,mynums):\n", "    print(n)"]}, {"cell_type": "markdown", "id": "bf37cdbe", "metadata": {}, "source": ["**`lambda` in Python**\n", "\n", "A `lambda` function is a small anonymous function defined using the `lambda` keyword. It can have any number of arguments but only one expression, which is evaluated and returned.\n", "\n", "- **Syntax**: `lambda arguments: expression`\n", "- **arguments**: The input parameters.\n", "- **expression**: The expression that is evaluated and returned.\n", "\n", "`lambda` functions are commonly used for short, throwaway functions that are used once or twice in the code.\n"]}, {"cell_type": "code", "execution_count": 87, "id": "a9181319", "metadata": {}, "outputs": [], "source": ["square = lambda num: num ** 2"]}, {"cell_type": "code", "execution_count": 88, "id": "4c6bf7f9", "metadata": {}, "outputs": [{"data": {"text/plain": ["25"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["square(5)"]}, {"cell_type": "code", "execution_count": 90, "id": "89523e3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25]"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(lambda num: num ** 2, my_nums))"]}, {"cell_type": "code", "execution_count": 91, "id": "220ac554", "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON>', '<PERSON>', '<PERSON>']"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["names"]}, {"cell_type": "code", "execution_count": 93, "id": "87217717", "metadata": {}, "outputs": [{"data": {"text/plain": ["['A', 'E', 'S']"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(lambda x:x[0], names))"]}, {"cell_type": "markdown", "id": "fcbbb609", "metadata": {}, "source": ["# Nested Statements and Scope"]}, {"cell_type": "code", "execution_count": 95, "id": "fb0a978a", "metadata": {}, "outputs": [], "source": ["X = 25\n", "\n", "def printer():\n", "    X = 50\n", "    return X"]}, {"cell_type": "code", "execution_count": 98, "id": "2dc09227", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["print(X)"]}, {"cell_type": "code", "execution_count": 99, "id": "930cee5c", "metadata": {}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["printer()"]}, {"cell_type": "markdown", "id": "7bb4a7ac", "metadata": {}, "source": ["**LEGB Rule in Python**\n", "\n", "The LEGB rule defines the order in which Python looks up variable names:\n", "\n", "- **L (Local)**: Names assigned within a function.\n", "- **E (Enclosing)**: Names in enclosing (outer) functions.\n", "- **G (Global)**: Names assigned at the top level of a module or declared global.\n", "- **B (Built-in)**: Names preassigned by Python (like `len`, `range`).\n", "\n", "Python searches in this order: Local → Enclosing → Global → Built-in.\n"]}, {"cell_type": "code", "execution_count": 101, "id": "f6a09403", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello sammy\n"]}], "source": ["name = 'This is global name'\n", "\n", "def greet():\n", "    name = 'sammy'\n", "    \n", "    def hello():\n", "        print(f'Hello {name}')\n", "\n", "    hello()\n", "\n", "greet()"]}, {"cell_type": "code", "execution_count": 102, "id": "906f5c64", "metadata": {}, "outputs": [], "source": ["X = 50\n", "\n", "def func():\n", "    global X\n", "    X = 100\n", "    return X"]}, {"cell_type": "code", "execution_count": 103, "id": "5c86f6b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}, {"cell_type": "code", "execution_count": 104, "id": "df212049", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["func()"]}, {"cell_type": "code", "execution_count": 105, "id": "deefccaa", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}