{"cells": [{"cell_type": "markdown", "id": "1e28473e", "metadata": {}, "source": ["# Task: Recursive None Counter\n", "\n", "## Problem Statement\n", "Write a Python function that counts `None` values in a list **recursively**.  \n", "If the list is empty, the function should return `0`.\n", "\n", "## Steps\n", "1. Define a function that accepts a list as input.\n", "2. Check if the list is empty – if yes, return 0.\n", "3. Check if the first element is `None`. If it is, count it as 1.\n", "4. Recursively call the function on the rest of the list.\n", "5. Add the result to the count and return it.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "90cca913", "metadata": {}, "outputs": [], "source": ["def count_none(input_list):\n", "    if not input_list:\n", "        return 0\n", "    \n", "    head, *tail = input_list\n", "    if head is None:\n", "        return 1 + count_none(tail)\n", "    else:\n", "        return count_none(tail)"]}, {"cell_type": "code", "execution_count": 2, "id": "6e0e5378", "metadata": {}, "outputs": [], "source": ["def main():\n", "    try:\n", "        values_list = [None, \"Red\", None, 8, None, True]\n", "        result = count_none(values_list)\n", "        \n", "        print(\"Number of None values:\", result)\n", "    except Exception as e:\n", "        print(\"An error occurred:\", e)"]}, {"cell_type": "code", "execution_count": 3, "id": "a5febb0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of None values: 3\n"]}], "source": ["if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}