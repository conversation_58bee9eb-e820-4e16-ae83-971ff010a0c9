{"cells": [{"cell_type": "markdown", "id": "b3b5553d", "metadata": {}, "source": ["# Task: KMP Algorithm for Pattern Searching\n", "\n", "## Problem Statement:\n", "Given a string `txt` of length `n` and a pattern `pat` of length `m`, implement the **K<PERSON><PERSON>-<PERSON> (KMP) algorithm** to find all occurrences of the pattern `pat` in the string `txt`.\n", "\n", "## Steps:\n", "1. **Preprocess the Pattern**: Build the **Longest Prefix Suffix (LPS)** array for the pattern.\n", "2. **Pattern Matching**: Use the LPS array to skip unnecessary comparisons and match the pattern efficiently.\n", "3. **Time Complexity**: The time complexity is **O(n + m)**, where `n` is the length of the text and `m` is the length of the pattern.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6747ca1b", "metadata": {}, "outputs": [], "source": ["def <PERSON><PERSON><PERSON><PERSON><PERSON>(pat, txt):\n", "    M = len(pat)\n", "    N = len(txt)\n", "    lps = [0] * M\n", "    j = 0\n", "    computeLPSArray(pat, M, lps)\n", "\n", "    i = 0\n", "    while i < N:\n", "        if pat[j] == txt[i]:\n", "            i += 1\n", "            j += 1\n", "\n", "        if j == M:\n", "            print(\"Found pattern at index\", i - j)\n", "            j = lps[j - 1]\n", "\n", "        elif i < N and pat[j] != txt[i]:\n", "            if j != 0:\n", "                j = lps[j-1]\n", "            else:\n", "                i += 1"]}, {"cell_type": "code", "execution_count": 2, "id": "0c008a6e", "metadata": {}, "outputs": [], "source": ["def computeLPSArray(pat, M, lps):\n", "    len = 0\n", "    lps[0] = 0\n", "    i = 1\n", "\n", "    while i < M:\n", "        if pat[i] == pat[len]:\n", "            len += 1\n", "            lps[i] = len\n", "            i += 1\n", "        else:\n", "            if len != 0:\n", "                len = lps[len - 1]\n", "            else:\n", "                lps[i] = 0\n", "                i += 1"]}, {"cell_type": "code", "execution_count": 3, "id": "5fb945b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found pattern at index 10\n"]}], "source": ["txt = \"ABABDABACDABABCABAB\"\n", "pat = \"ABABCABAB\"\n", "KMPSearch(pat, txt)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}