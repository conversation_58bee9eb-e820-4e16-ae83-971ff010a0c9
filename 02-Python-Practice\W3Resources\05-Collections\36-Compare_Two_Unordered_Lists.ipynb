{"cells": [{"cell_type": "markdown", "id": "86409c18", "metadata": {}, "source": ["# Task: Compare Two Unordered Lists (Not Sets)\n", "\n", "## Problem Statement:\n", "Write a Python program to compare **two unordered lists** to check if they contain the same elements with the same frequency, regardless of their order. Unlike sets, duplicate elements matter and must be accounted for.\n", "\n", "## Steps:\n", "1. Accept or define the two input lists.\n", "2. Use the `collections.Counter` class to count the frequency of each element in both lists.\n", "3. <PERSON><PERSON><PERSON> the two Counter objects.\n", "4. Return or print whether the lists are equal based on their content and frequency.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fb3789e0", "metadata": {}, "outputs": [], "source": ["from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "id": "e1bd2405", "metadata": {}, "outputs": [], "source": ["def compare_lists(x, y):\n", "    return Counter(x) == Counter(y)"]}, {"cell_type": "code", "execution_count": 3, "id": "f8876a26", "metadata": {}, "outputs": [], "source": ["n1 = [20, 10, 30, 10, 20, 30]\n", "n2 = [30, 20, 10, 30, 20, 50]"]}, {"cell_type": "code", "execution_count": 4, "id": "0a0539f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["print(compare_lists(n1, n2))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}