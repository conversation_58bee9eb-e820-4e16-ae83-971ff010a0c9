{"cells": [{"cell_type": "markdown", "id": "a7a29f24", "metadata": {}, "source": ["# Task: Pretty Print Linked List in Python\n", "\n", "## Problem Statement:\n", "When implementing a custom data structure like a linked list, the default print behavior may not be readable. The goal is to make printing linked list objects more intuitive and readable, such as printing `10->15->20` as `[10, 15, 20]` using Python’s special (dunder) methods.\n", "\n", "## Steps:\n", "\n", "1. Define a **Node class** to represent each node in the list.\n", "2. Define a **LinkedList class** to manage the nodes.\n", "3. Use the `__str__()` dunder method in the `LinkedList` class.\n", "4. In the `__str__()` method:\n", "   - Traverse the linked list from the head.\n", "   - Collect the data from each node into a Python list.\n", "   - Return the string representation of that list.\n", "5. Now, when the `LinkedList` object is printed, it shows a clean, human-readable format like `[10, 15, 20]`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8d6e464d", "metadata": {}, "outputs": [], "source": ["class Node:\n", "    def __init__(self, val=None):\n", "        self.val = val\n", "        self.next = None"]}, {"cell_type": "code", "execution_count": 2, "id": "b6d9b4a7", "metadata": {}, "outputs": [], "source": ["class LinkedList:\n", "    def __init__(self,head=None):\n", "        self.head = head\n", "\n", "    def __str__(self):\n", "        res = \"\"\n", "        ptr = self.head\n", "        while ptr:\n", "            res += str(ptr.val) + \", \"\n", "            ptr = ptr.next\n", "        res = res.strip(\", \")\n", "        if len(res):\n", "            return \"[\" + res + \"]\"\n", "        else:\n", "            return \"[]\""]}, {"cell_type": "code", "execution_count": 3, "id": "1ff45c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[10, 15, 20]\n"]}], "source": ["if __name__ == \"__main__\":\n", "    ll = LinkedList()\n", "    node1 = Node(10)\n", "    node2 = Node(15)\n", "    node3 = Node(20)\n", "    ll.head = node1\n", "    node1.next = node2\n", "    node2.next = node3\n", "    print(ll)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}