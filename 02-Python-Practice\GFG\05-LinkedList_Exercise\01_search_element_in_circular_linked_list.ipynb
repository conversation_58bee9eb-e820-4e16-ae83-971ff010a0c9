{"cells": [{"cell_type": "markdown", "id": "6cea406a-6a84-4ff2-89e6-d118050d87fd", "metadata": {}, "source": ["# Task: Search an Element in a Circular Linked List using Python\n", "\n", "## Problem Statement:\n", "Given a circular linked list, write a Python program to search for a specific element in the list. A circular linked list is one in which the last node points back to the first node, forming a loop.\n", "\n", "## Steps:\n", "1. Initialize a node pointer `temp = head`.\n", "2. Set a flag variable `f = 0` to track whether the element is found.\n", "3. Check if the list is empty (`head is None`), and print \"List is empty\" if so.\n", "4. Traverse the list starting from `head` using a loop until the node points back to `head`.\n", "5. For each node, compare the node's data with the target element:\n", "   - If found, set `f = 1` and break.\n", "6. After traversal:\n", "   - If `f == 0`, print \"Element not found\".\n", "   - <PERSON><PERSON>, print \"Element found\".\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9ef5e930-9c1e-480f-a6e9-4f876bae2740", "metadata": {}, "outputs": [], "source": ["class Node:\n", "    def __init__(self,data):\n", "        self.data = data\n", "        self.next = None"]}, {"cell_type": "code", "execution_count": 2, "id": "b3762580-b114-4c69-9fcb-876c9680c598", "metadata": {}, "outputs": [], "source": ["class CircularLinkedList:\n", "\n", "    def __init__(self):\n", "        self.head = Node(None)\n", "        self.tail = Node(None)\n", "        self.head.next = self.tail\n", "        self.tail.next = self.head\n", "\n", "    def add(self,data):\n", "\n", "        newNode = Node(data)\n", "\n", "        if self.head.data is None:\n", "            self.head = newNode\n", "            self.tail = newNode\n", "            newNode.next = self.head\n", "\n", "        else:\n", "            self.tail.next = newNode\n", "            self.tail = newNode\n", "            self.tail.next = self.head\n", "\n", "    def findNode(self, element):\n", "        current = self.head\n", "        i = 1\n", "        f = 0    \n", "        if(self.head == None):\n", "            print(\"Empty list\") \n", "        else:\n", "            while(True):     \n", "                if(current.data ==  element): \n", "                    f += 1    \n", "                    break\n", "                current = current.next    \n", "                i = i + 1    \n", "                if(current == self.head):    \n", "                    break    \n", "            if(f > 0):    \n", "                print(\"element is present\")    \n", "            else:    \n", "                print(\"element is not present\")    "]}, {"cell_type": "code", "execution_count": 3, "id": "14a650e9-0cf5-4215-a4af-96694aff38b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["element is present\n", "element is not present\n"]}], "source": ["if __name__ == '__main__':\n", "    circularLinkedList = CircularLinkedList()\n", "    circularLinkedList.add(1)\n", "    circularLinkedList.add(2)\n", "    circularLinkedList.add(3)\n", "    circularLinkedList.add(4)\n", "    circularLinkedList.add(5)\n", "    circularLinkedList.add(6)\n", "    \n", "    circularLinkedList.findNode(2)\n", "    circularLinkedList.findNode(7)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}