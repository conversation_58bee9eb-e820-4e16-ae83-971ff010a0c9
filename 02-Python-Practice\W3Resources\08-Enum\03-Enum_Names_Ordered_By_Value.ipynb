{"cells": [{"cell_type": "markdown", "id": "bd191ea9", "metadata": {}, "source": ["# Task: Display Enum Member Names Ordered by Their Values\n", "\n", "## Problem Statement:\n", "Write a Python program to define an `Enum` class and display **all the member names** ordered by their **associated values**.\n", "\n", "## Steps:\n", "1. **Import** the `Enum` class from the `enum` module.\n", "2. **Create an Enum class** with multiple members and associated values.\n", "3. **Sort the enum members** using the `sorted()` function with `key=lambda x: x.value`.\n", "4. **Print the names** of the sorted enum members.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5dd9a081", "metadata": {}, "outputs": [], "source": ["from enum import Enum"]}, {"cell_type": "code", "execution_count": 2, "id": "52649f14", "metadata": {}, "outputs": [], "source": ["class Country(Enum):\n", "    Afghanistan = 93\n", "    Albania = 355\n", "    Algeria = 213\n", "    Andorra = 376\n", "    Angola = 244\n", "    Antarctica = 672"]}, {"cell_type": "code", "execution_count": 3, "id": "29c732bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Country Name ordered by Country Code:\n", "Afghanistan\n", "Algeria\n", "Angola\n", "Albania\n", "Andorra\n", "Antarctica\n"]}], "source": ["print(\"Country Name ordered by Country Code:\")\n", "for country in sorted(Country, key=lambda x: x.value):\n", "    print(country.name)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}