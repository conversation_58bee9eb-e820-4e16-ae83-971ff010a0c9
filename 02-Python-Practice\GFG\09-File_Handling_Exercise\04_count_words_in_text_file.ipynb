{"cells": [{"cell_type": "markdown", "id": "ac04e999", "metadata": {}, "source": ["# Task: Count Words in Text File in Python\n", "\n", "## Problem Statement:\n", "Create a Python program to read the contents of a `.txt` file, count the total number of words present in the file, and display the word count.\n", "\n", "## Steps:\n", "1. Open the text file in read mode using the `open()` function.\n", "2. Read the entire content of the file using `.read()`.\n", "3. Use the `.split()` method to split the content into individual words.\n", "4. Use the `len()` function to count the number of words.\n", "5. Print the word count.\n"]}, {"cell_type": "markdown", "id": "86cbd51b", "metadata": {}, "source": ["## Count String Words"]}, {"cell_type": "code", "execution_count": 1, "id": "08f99829", "metadata": {}, "outputs": [], "source": ["c = 0"]}, {"cell_type": "code", "execution_count": 2, "id": "8d2a14b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["with open(r'input.txt','r') as file:\n", "\n", "    data = file.read()\n", "    \n", "    w = data.split()\n", "    \n", "    c += len(w)\n", "\n", "print(c)"]}, {"cell_type": "markdown", "id": "dbc752cb", "metadata": {}, "source": ["## Count the number of words, not Integer"]}, {"cell_type": "code", "execution_count": 3, "id": "2ccef86a", "metadata": {}, "outputs": [], "source": ["c = 0"]}, {"cell_type": "code", "execution_count": 4, "id": "87a00a98", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24\n"]}], "source": ["with open(r'input.txt','r') as file:\n", "\n", "    data = file.read()\n", "\n", "    lines = data.split()\n", "\n", "    # Iterating over every word in lines\n", "    for word in lines:\n", "\n", "        # checking if the word is numeric or not\n", "        if not word.isnumeric():          \n", "\n", "            c += 1\n", "\n", "print(c)"]}, {"cell_type": "markdown", "id": "3d2b5f64", "metadata": {}, "source": ["# Task: Check File Size in Python\n", "\n", "## Problem Statement:\n", "Write a Python script that takes a file as input and prints its size in bytes. This can be achieved using standard libraries like `os` and `pathlib`.\n", "\n", "## Steps:\n", "1. **Import the required module**: Use either the `os` module or `pathlib` module.\n", "2. **Specify the file path**.\n", "3. **Use one of the following methods**:\n", "   - With `os.path.getsize(path)`: Returns the size of the file in bytes.\n", "   - With `Path(file).stat().st_size` from the `pathlib` module.\n", "4. **Print the file size**.\n"]}, {"cell_type": "markdown", "id": "9e33508c", "metadata": {}, "source": ["## Method1: Using pathlib"]}, {"cell_type": "code", "execution_count": 5, "id": "a8909fcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["172\n"]}], "source": ["from pathlib import Path\n", "\n", "sz = Path('input.txt').stat().st_size\n", "\n", "print(sz)"]}, {"cell_type": "markdown", "id": "b3ea8665", "metadata": {}, "source": ["## Method 2: With Os module"]}, {"cell_type": "code", "execution_count": 6, "id": "aadbe3cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["172\n"]}], "source": ["import os\n", "\n", "sz = os.path.getsize(\"input.txt\")\n", "\n", "print(sz)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}