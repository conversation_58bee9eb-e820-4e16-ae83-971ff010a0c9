{"cells": [{"cell_type": "markdown", "id": "d8ce155c", "metadata": {}, "source": ["# Task: Target Indices in Uneven Matrix\n", "\n", "## Problem Statement:\n", "Given an **uneven (ragged) matrix** (a list of lists where sublists may have different lengths) and a **target value**, write a Python program to find and return the **indices (row, column)** of all occurrences of the target value.\n", "\n", "## Steps:\n", "1. **Initialize an empty list** to store the result indices.\n", "2. **Iterate over each row** in the matrix using a loop.\n", "3. **Iterate over each element** in the current row and check if it matches the target.\n", "4. If a match is found, **append the (row_index, col_index)** to the result list.\n", "5. **Return or print** the final list of matching indices.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "27cea762", "metadata": {}, "outputs": [], "source": ["def target_indices(matrix, target):\n", "    res = []\n", "\n", "    for i in range(len(matrix)):\n", "        for j in range(len(matrix[i])):\n", "            if matrix[i][j] == target:\n", "                res.append((i,j))\n", "    return res"]}, {"cell_type": "code", "execution_count": 17, "id": "ca3fe32f", "metadata": {}, "outputs": [], "source": ["matrix = [[1, 3, 2, 32, 19], [19, 2, 48, 19], [], [9, 35, 4], [3, 19]]\n", "target = 19"]}, {"cell_type": "code", "execution_count": 18, "id": "2b8123a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(0, 4), (1, 0), (1, 3), (4, 1)]\n"]}], "source": ["print(target_indices(matrix, target))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}