{"cells": [{"cell_type": "markdown", "id": "7386b932", "metadata": {}, "source": ["# Task: Python Program for Binary Insertion Sort\n", "\n", "## Problem Statement:\n", "Implement **Binary Insertion Sort**, an optimization over regular Insertion Sort that uses **Binary Search** to find the correct position for inserting an element, reducing the number of comparisons from O(i) to O(log i) at each iteration.\n", "\n", "## Steps:\n", "1. Start from the second element (index 1) and iterate through the array.\n", "2. For each element, use **Binary Search** to find the correct position in the sorted part of the array (from index 0 to current index - 1).\n", "3. Once the correct index is found, shift all elements after that index by one to the right to make space.\n", "4. Insert the current element at the found position.\n", "5. Repeat until the entire array is sorted.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "679ff11d", "metadata": {}, "outputs": [], "source": ["def binary_search(arr, val,start, end):\n", "    if start == end:\n", "        if arr[start] > val:\n", "            return start\n", "        else:\n", "            return start+1\n", "    if start > end:\n", "        return start\n", "    mid = (start+end)//2\n", "    if arr[mid] < val:\n", "        return binary_search(arr, val, mid+1, end)\n", "    elif arr[mid] > val:\n", "        return binary_search(arr, val, start, mid-1)\n", "    else:\n", "        return mid"]}, {"cell_type": "code", "execution_count": 2, "id": "99da9448", "metadata": {}, "outputs": [], "source": ["def insertion_sort(arr):\n", "    for i in range(1, len(arr)):\n", "        val = arr[i]\n", "        j = binary_search(arr, val, 0, i-1)\n", "        arr = arr[:j] + [val] + arr[j:i] + arr[i+1:]\n", "    return arr"]}, {"cell_type": "code", "execution_count": 3, "id": "0cf3c130", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sorted array:\n", "[0, 12, 17, 23, 31, 37, 46, 54, 72, 88, 100]\n"]}], "source": ["print(\"Sorted array:\")\n", "print(insertion_sort([37, 23, 0, 17, 12, 72, 31, 46, 100, 88, 54]))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}