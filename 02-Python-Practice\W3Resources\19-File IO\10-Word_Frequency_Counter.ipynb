{"cells": [{"cell_type": "markdown", "id": "89451466", "metadata": {}, "source": ["# Task: Word Frequency Counter\n", "\n", "## Problem Statement:\n", "Write a Python program to **read a text file** and **count the frequency** of each word present in it. The program should display how many times each word appears.\n", "\n", "## Steps:\n", "1. **Open the file** in read mode and read its content.\n", "2. **Convert the text to lowercase** to make the count case-insensitive.\n", "3. **Remove punctuation** using regex or string methods.\n", "4. **Split the text into words**.\n", "5. Use a **dictionary or `collections.Counter`** to count the frequency of each word.\n", "6. **Display or print** the word-frequency pairs.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "45cb2aaa", "metadata": {}, "outputs": [], "source": ["from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "id": "1554011c", "metadata": {}, "outputs": [], "source": ["def word_count(fname):\n", "        with open(fname) as f:\n", "                return Counter(f.read().split())"]}, {"cell_type": "code", "execution_count": 3, "id": "3da2c97f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of words in the file : Counter({'and': 6, 'is': 3, 'Python': 3, 'a': 3, 'programming': 3, 'dynamic': 2, 'code': 2, 'to': 2, 'in': 2, 'or': 2, 'What': 1, 'language?': 1, 'widely': 1, 'used': 1, 'high-level,': 1, 'general-purpose,': 1, 'interpreted,': 1, 'language.': 1, 'Its': 1, 'design': 1, 'philosophy': 1, 'emphasizes': 1, 'readability,': 1, 'its': 1, 'syntax': 1, 'allows': 1, 'programmers': 1, 'express': 1, 'concepts': 1, 'fewer': 1, 'lines': 1, 'of': 1, 'than': 1, 'possible': 1, 'languages': 1, 'such': 1, 'as': 1, 'C++': 1, 'Java.': 1, 'supports': 1, 'multiple': 1, 'paradigms,': 1, 'including': 1, 'object-oriented,': 1, 'imperative': 1, 'functional': 1, 'procedural': 1, 'styles.It': 1, 'features': 1, 'type': 1, 'system': 1, 'automatic': 1, 'memory': 1, 'management': 1, 'has': 1, 'large': 1, 'comprehensive': 1, 'standard': 1, 'library.': 1, 'The': 1, 'best': 1, 'way': 1, 'we': 1, 'learn': 1, 'anything': 1, 'by': 1, 'practice': 1, 'exercise': 1, 'questions.': 1, 'We': 1, 'have': 1, 'started': 1, 'this': 1, 'section': 1, 'for': 1, 'those': 1, '(beginner': 1, 'intermediate)': 1, 'who': 1, 'are': 1, 'familiar': 1, 'with': 1, 'Python.': 1})\n"]}], "source": ["print(\"Number of words in the file :\",word_count(\"test.txt\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}