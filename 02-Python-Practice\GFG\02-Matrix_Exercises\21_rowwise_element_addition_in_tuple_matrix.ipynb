{"cells": [{"cell_type": "markdown", "id": "36ed31f5-fb54-47dc-9248-ef027e5315cc", "metadata": {}, "source": ["# Task: Row-wise Element Addition in Tuple Matrix\n", "\n", "## Problem Statement:\n", "Given a tuple matrix and a list of custom values, perform row-wise addition by adding the corresponding custom value to each element in its respective row.\n", "\n", "### Steps:\n", "1. Use `enumerate()` to iterate through each row along with its index.\n", "2. For each row, use nested list comprehension to add the corresponding custom value to each element.\n", "3. Convert the resulting elements back into tuples to maintain the matrix structure.\n", "4. Collect all updated rows into a new matrix.\n", "5. Return the modified matrix with updated tuple values.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5b6c6bac-70e9-475f-bd13-697d2f948289", "metadata": {}, "outputs": [], "source": ["def row_wise_element_addition(test_list, cus_eles):\n", "    return [[sub + (cus_eles[idx], ) for sub in val] for idx, val in enumerate(test_list)]"]}, {"cell_type": "code", "execution_count": 2, "id": "09d894dc-02fb-46e3-9c5f-6f15e3be9421", "metadata": {}, "outputs": [], "source": ["test_list = [[('Gfg', 3), ('is', 3)], [('best', 1)], [('for', 5), ('geeks', 1)]]"]}, {"cell_type": "code", "execution_count": 3, "id": "f2797b94-ad74-4944-b8ad-05dc6b35e75a", "metadata": {}, "outputs": [], "source": ["cus_eles = [6, 7, 8]"]}, {"cell_type": "code", "execution_count": 4, "id": "d8ae891e-e452-4c97-bb26-704dfc13cdbb", "metadata": {}, "outputs": [], "source": ["result = row_wise_element_addition(test_list, cus_eles)"]}, {"cell_type": "code", "execution_count": 5, "id": "f6324d83-9eee-4982-b915-11de31c57de3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The matrix after row elements addition: [[('Gfg', 3, 6), ('is', 3, 6)], [('best', 1, 7)], [('for', 5, 8), ('geeks', 1, 8)]]\n"]}], "source": ["print(\"The matrix after row elements addition:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}