{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Advanced Python Objects Test"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Numbers\n", "\n", "**Problem 1: Convert 1024 to binary and hexadecimal representation**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0x400\n", "0b10000000000\n"]}], "source": ["print(hex(1024))\n", "print(bin(1024))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 2: Round 5.23222 to two decimal places**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.23"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["round(5.23222 , 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Strings\n", "**Problem 3: Check if every letter in the string s is lower case**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["s = 'hello how are you <PERSON>, are you feeling okay?'\n", "print(s.islower())\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 4: How many times does the letter 'w' show up in the string below?**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["s = 'twywywtwywbwhsjhwuwshshwuwwwjdjdid'\n", "s.count(\"w\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Sets\n", "**Problem 5: Find the elements in set1 that are not in set2:**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{2}\n"]}], "source": ["set1 = {2,3,1,5,6,8}\n", "set2 = {3,1,7,5,6,8}\n", "print(set1.difference(set2))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 6: Find all elements that are in either set:**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 5, 6, 7, 8}\n"]}], "source": ["print(set1.union(set2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Dictionaries\n", "\n", "**Problem 7: Create this dictionary:\n", "{0: 0, 1: 1, 2: 8, 3: 27, 4: 64}\n", " using a dictionary comprehension.**"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 0, 1: 1, 2: 8, 3: 27, 4: 64}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["d = {x : x**3 for x in range(5)}\n", "d"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Lists\n", "\n", "**Problem 8: Reverse the list below:**"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4, 3, 2, 1]\n"]}], "source": ["list1 = [1,2,3,4]\n", "list1.reverse()\n", "print(list1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 9: Sort the list below:**"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n"]}], "source": ["list2 = [3,4,2,5,1]\n", "list2.sort()\n", "print(list2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Great Job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}