{"cells": [{"cell_type": "markdown", "id": "ecf61e4e-56c0-475a-9373-3a4e1c68e01a", "metadata": {}, "source": ["# Module 21: Introduction to GUIs"]}, {"cell_type": "code", "execution_count": 1, "id": "57d56105-dc94-45fb-a481-795d7ffabc12", "metadata": {}, "outputs": [], "source": ["from ipywidgets import interact, interactive, fixed\n", "import ipywidgets as widgets"]}, {"cell_type": "code", "execution_count": 2, "id": "71f165d4-0e56-4394-854c-8aa2ab2b637a", "metadata": {}, "outputs": [], "source": ["def func(x):\n", "    return x"]}, {"cell_type": "code", "execution_count": 3, "id": "de07b809-7ccc-46aa-8f89-d137bbf734c1", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a0ce1858b55489abf49aec37f6ba320", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=10, description='x', max=30, min=-10), Output()), _dom_classes=('widget-…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=10)"]}, {"cell_type": "code", "execution_count": 4, "id": "99c58449-c6ab-46be-aa1c-f045715a5716", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "42f986a8933b4784ba2d2680458b6e2a", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Checkbox(value=True, description='x'), Output()), _dom_classes=('widget-interact',))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "08f3c306-fbea-46b9-8a31-1b8b96c4d733", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b8d4403cb494ca28e289b14f913b4db", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Checkbox(value=True, description='x'), Output()), _dom_classes=('widget-interact',))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@interact(x=True,y=fixed(1.0))\n", "def g(x,y):\n", "    return (x,y)"]}, {"cell_type": "code", "execution_count": 6, "id": "77141e27-8126-4059-8b26-7577616bddd0", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2b2514b82ad34b979a13c5e78873371f", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=0, description='x', min=-100), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=widgets.IntSlider(min=-100,max=100,step=1,value=0))"]}, {"cell_type": "code", "execution_count": 7, "id": "c92479bb-38c6-4a71-8be7-f080cdadfa91", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e69ac969a31497dba17e98016c6887b", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=0, description='x', max=10, min=-10), Output()), _dom_classes=('widget-i…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=(-10,10,1))"]}, {"cell_type": "code", "execution_count": 8, "id": "d8b1fdd6-ecef-4c4d-a445-19ab225b9544", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ce7f8d41f6694252bec1047d623d7dce", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(FloatSlider(value=0.0, description='x', max=10.0, min=-10.0), Output()), _dom_classes=('…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=(-10.0,10.0,.1))"]}, {"cell_type": "code", "execution_count": 9, "id": "70b6d3d0-180f-4e47-b252-17ce14ca54fc", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "56ed7396b6eb4d27a7f840e31d84beeb", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(FloatSlider(value=5.0, description='x', max=20.0, step=0.5), Output()), _dom_classes=('w…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@interact(x=(0.0,20.0,0.5))\n", "def h(x=5.0):\n", "    return x"]}, {"cell_type": "code", "execution_count": 10, "id": "38de7581-0f16-45c4-9fe3-fddb563cd4a1", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e66cc4c69bc54a378ef1994500f24558", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='x', options=('hello', 'how', 'are', 'you', '?'), value='hello'), O…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x=['hello','how','are','you','?'])"]}, {"cell_type": "code", "execution_count": 11, "id": "d8e02656-a8b8-459e-a576-50903704eb7d", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "61a65e33e8cb40c698a51de31d69a894", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='x', options={'one': 10, 'two': 20}, value=10), Output()), _dom_cla…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.func(x)>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["interact(func,x={'one':10,'two':20})"]}, {"cell_type": "code", "execution_count": 12, "id": "bf0f1bf6-4f4a-40e5-8757-4e6a57cbe6fc", "metadata": {}, "outputs": [], "source": ["from IPython.display import display\n", "\n", "def f(a,b):\n", "    display(a+b)\n", "    return(a+b)"]}, {"cell_type": "code", "execution_count": 13, "id": "bc4128e2-b193-48cf-b523-5ce1a6e28d67", "metadata": {}, "outputs": [], "source": ["w = interactive(f,a=10,b=20)"]}, {"cell_type": "code", "execution_count": 14, "id": "e9ac1579-1519-4b6f-bb6c-0849e1971862", "metadata": {}, "outputs": [{"data": {"text/plain": ["ipywidgets.widgets.interaction.interactive"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["type(w)"]}, {"cell_type": "code", "execution_count": 15, "id": "719a4cd6-5472-4b61-b238-96039d6b590b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(IntSlider(value=10, description='a', max=30, min=-10),\n", " IntSlider(value=20, description='b', max=60, min=-20),\n", " Output())"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["w.children"]}, {"cell_type": "code", "execution_count": 16, "id": "9cd1083f-6e8d-415b-88b8-162baaea4244", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "63cfb37c33de4b00a1f12a1eb06e9a6e", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=10, description='a', max=30, min=-10), IntSlider(value=20, description='…"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["w"]}, {"cell_type": "code", "execution_count": 17, "id": "06bafb68-0f95-4515-8621-669fa5f9e4fc", "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets"]}, {"cell_type": "code", "execution_count": 18, "id": "d5e5aadc-9b65-4ab0-9fc1-cc38d4a534ee", "metadata": {}, "outputs": [], "source": ["w = widgets.IntSlider()"]}, {"cell_type": "code", "execution_count": 19, "id": "a4d447c0-5935-46a8-acd4-fa7db46fa87e", "metadata": {}, "outputs": [], "source": ["from IPython.display import display"]}, {"cell_type": "code", "execution_count": 20, "id": "b5c638ac-2e68-4eb9-b892-8bffa8617686", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "94833ef47d76448899d9830d274298d1", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(w)"]}, {"cell_type": "code", "execution_count": 21, "id": "e3388ae6-b4a0-46d3-bf53-39f172966b5f", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "94833ef47d76448899d9830d274298d1", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(w)"]}, {"cell_type": "code", "execution_count": 22, "id": "21984c3e-59bd-4e7a-b884-f4677e02dc97", "metadata": {}, "outputs": [], "source": ["w.close()"]}, {"cell_type": "code", "execution_count": 23, "id": "bab96ad1-4856-4aa3-b40c-d06d545837d5", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "81e03ce5e98440ddab8fd43991c06843", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["w = widgets.IntSlider()\n", "w"]}, {"cell_type": "code", "execution_count": 24, "id": "17577d3c-f270-4876-a7ba-ca6c11d3c8a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["w.value"]}, {"cell_type": "code", "execution_count": 25, "id": "834c5c7b-4ad4-47b6-9507-a139eadfd9ef", "metadata": {}, "outputs": [], "source": ["w.value = 50"]}, {"cell_type": "code", "execution_count": 26, "id": "9bc9774b-29de-4633-a835-91711e87e643", "metadata": {}, "outputs": [{"data": {"text/plain": ["['_dom_classes',\n", " '_model_module',\n", " '_model_module_version',\n", " '_model_name',\n", " '_view_count',\n", " '_view_module',\n", " '_view_module_version',\n", " '_view_name',\n", " 'behavior',\n", " 'continuous_update',\n", " 'description',\n", " 'description_allow_html',\n", " 'disabled',\n", " 'layout',\n", " 'max',\n", " 'min',\n", " 'orientation',\n", " 'readout',\n", " 'readout_format',\n", " 'step',\n", " 'style',\n", " 'tabbable',\n", " 'tooltip',\n", " 'value']"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["w.keys"]}, {"cell_type": "code", "execution_count": 27, "id": "67897bd9-b378-4632-8ed7-253228ccd5a5", "metadata": {}, "outputs": [], "source": ["w.max = 2000"]}, {"cell_type": "code", "execution_count": 28, "id": "bd4e2d70-d23b-4687-a28e-680d40aa71ef", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9d1c51d6b87f4c9b895cd659ddd4c69a", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatText(value=0.0)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "14e34b74325d4b6a9fdf720dba61fbe4", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatSlider(value=0.0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a = widgets.FloatText()\n", "b = widgets.FloatSlider()\n", "\n", "display(a,b)\n", "\n", "mylink = widgets.jslink((a,'value'),(b,'value'))"]}, {"cell_type": "code", "execution_count": 29, "id": "a11a443d-6c19-4509-8992-eb6c84005c5d", "metadata": {}, "outputs": [], "source": ["mylink.unlink()"]}, {"cell_type": "markdown", "id": "0ae484ee-2c29-489f-8581-eb55aada314f", "metadata": {}, "source": ["**Complete List of Jupyter Widgets**"]}, {"cell_type": "code", "execution_count": 30, "id": "40c2c9d7-b68f-42e3-a645-1e1838f82405", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Accordion',\n", " 'AppLayout',\n", " 'Audio',\n", " 'BoundedFloatText',\n", " 'BoundedIntText',\n", " 'Box',\n", " 'Button',\n", " 'ButtonStyle',\n", " 'Callback<PERSON><PERSON><PERSON>tcher',\n", " 'Checkbox',\n", " 'Color',\n", " 'ColorPicker',\n", " 'ColorsInput',\n", " 'Combobox',\n", " 'Controller',\n", " 'CoreWidget',\n", " 'DOMWidget',\n", " 'DatePicker',\n", " 'Datetime',\n", " 'DatetimePicker',\n", " 'Dropdown',\n", " 'FileUpload',\n", " 'FloatLogSlider',\n", " 'FloatProgress',\n", " 'FloatRangeSlider',\n", " 'FloatSlider',\n", " 'FloatText',\n", " 'FloatsInput',\n", " 'GridBox',\n", " 'GridspecLayout',\n", " 'HBox',\n", " 'HTML',\n", " 'HTMLMath',\n", " 'Image',\n", " 'IntProgress',\n", " 'IntRangeSlider',\n", " 'IntSlider',\n", " 'IntText',\n", " 'IntsInput',\n", " 'Label',\n", " 'Layout',\n", " 'NaiveDatetimePicker',\n", " 'NumberFormat',\n", " 'Output',\n", " 'Password',\n", " 'Play',\n", " 'RadioButtons',\n", " 'Select',\n", " 'SelectMultiple',\n", " 'SelectionRangeSlider',\n", " 'SelectionSlider',\n", " 'SliderStyle',\n", " 'Stack',\n", " 'Style',\n", " 'Tab',\n", " 'TagsInput',\n", " 'Text',\n", " 'Textarea',\n", " 'TimePicker',\n", " 'ToggleButton',\n", " 'ToggleButtons',\n", " 'ToggleButtonsStyle',\n", " 'TwoByTwoLayout',\n", " 'TypedTuple',\n", " 'VBox',\n", " 'Valid',\n", " 'ValueWidget',\n", " 'Video',\n", " 'Widget',\n", " '__builtins__',\n", " '__cached__',\n", " '__doc__',\n", " '__file__',\n", " '__jupyter_widgets_base_version__',\n", " '__jupyter_widgets_controls_version__',\n", " '__loader__',\n", " '__name__',\n", " '__package__',\n", " '__path__',\n", " '__protocol_version__',\n", " '__spec__',\n", " '__version__',\n", " '_handle_ipython',\n", " '_version',\n", " 'comm',\n", " 'dlink',\n", " 'docutils',\n", " 'domwidget',\n", " 'fixed',\n", " 'get_ipython',\n", " 'interact',\n", " 'interact_manual',\n", " 'interaction',\n", " 'interactive',\n", " 'interactive_output',\n", " 'jsdlink',\n", " 'jslink',\n", " 'link',\n", " 'load_ipython_extension',\n", " 'os',\n", " 'register',\n", " 'register_comm_target',\n", " 'sys',\n", " 'trait_types',\n", " 'utils',\n", " 'valuewidget',\n", " 'widget',\n", " 'widget_bool',\n", " 'widget_box',\n", " 'widget_button',\n", " 'widget_color',\n", " 'widget_controller',\n", " 'widget_core',\n", " 'widget_date',\n", " 'widget_datetime',\n", " 'widget_description',\n", " 'widget_float',\n", " 'widget_int',\n", " 'widget_layout',\n", " 'widget_link',\n", " 'widget_media',\n", " 'widget_output',\n", " 'widget_selection',\n", " 'widget_selectioncontainer',\n", " 'widget_serialization',\n", " 'widget_string',\n", " 'widget_style',\n", " 'widget_tagsinput',\n", " 'widget_templates',\n", " 'widget_time',\n", " 'widget_upload',\n", " 'widgets']"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["import ipywidgets as widgets\n", "\n", "dir(widgets)"]}, {"cell_type": "code", "execution_count": 31, "id": "5c9bc6ac-cc90-4bb9-96e2-89c5c5c50b8e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "42c3057285024ae8bd3add3932a0b7c2", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["w = widgets.IntSlider()\n", "w"]}, {"cell_type": "code", "execution_count": 32, "id": "3fbfb47f-e5d1-4625-b623-742bf6e4934e", "metadata": {}, "outputs": [], "source": ["w.layout.margin = 'auto'\n", "w.layout.height = '75px'"]}, {"cell_type": "code", "execution_count": 33, "id": "5cafaadd-267b-4a3b-a533-a09f897b1b4c", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0b6ae8ce741b48c58696f6525d3ba0a2", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=15, description='New Slider')"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["x = widgets.IntSlider(value=15,description='New Slider')\n", "x"]}, {"cell_type": "code", "execution_count": 34, "id": "ba5d467b", "metadata": {}, "outputs": [], "source": ["x.layout = w.layout"]}, {"cell_type": "code", "execution_count": 37, "id": "61ff78a1-f141-4dbf-97f2-69c8d4135776", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f674452444d14698b532c83eef5d1460", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='info', description='Ordinary Button', style=ButtonStyle())"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["widgets.Button(description=\"<PERSON> Button\",button_style='info')"]}, {"cell_type": "code", "execution_count": 38, "id": "345842e7-c6f0-4356-b229-5205c29f1b68", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4bbc6388f81a411cb779509ba7c4bf0e", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='danger', description='Ordinary Button', style=ButtonStyle())"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["widgets.Button(description=\"<PERSON> Button\",button_style='danger')"]}, {"cell_type": "code", "execution_count": 40, "id": "d6d6e44c-1064-4c00-a03a-50d1e8b1fa38", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bc976e12d98541f7b184acca8fc8d90f", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(button_style='warning', description='Ordinary Button', style=ButtonStyle())"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["widgets.Button(description=\"<PERSON> Button\",button_style=\"warning\")"]}, {"cell_type": "code", "execution_count": 44, "id": "42f264a5-22e3-4945-95ae-3939ad503938", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "db1cf394a79f4fe7bedc33ec0558cfe4", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(description='Custom Color', style=ButtonStyle(button_color='Lightgreen'))"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["b1 = widgets.<PERSON><PERSON>(description=\"Custom Color\")\n", "b1.style.button_color = 'Lightgreen'\n", "b1"]}, {"cell_type": "code", "execution_count": 45, "id": "e58a7583-91af-44cf-a8e5-832bd0cef5b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["['_model_module',\n", " '_model_module_version',\n", " '_model_name',\n", " '_view_count',\n", " '_view_module',\n", " '_view_module_version',\n", " '_view_name',\n", " 'button_color',\n", " 'font_family',\n", " 'font_size',\n", " 'font_style',\n", " 'font_variant',\n", " 'font_weight',\n", " 'text_color',\n", " 'text_decoration']"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["b1.style.keys"]}, {"cell_type": "code", "execution_count": 46, "id": "4c0dd38d-0099-452b-a481-fd067174cd24", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4addb6674bf448748cf63b32e217c310", "version_major": 2, "version_minor": 0}, "text/plain": ["Button(description='New', style=ButtonStyle(button_color='Lightgreen'))"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["b2 = widgets.<PERSON><PERSON>(description=\"New\")\n", "b2.style = b1.style\n", "b2"]}, {"cell_type": "code", "execution_count": 50, "id": "919b6d8e-d3a0-40df-8f8a-4557bedbecb3", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "030dfcabd26446f09b129fc621b02f54", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0, description='My Handle', style=SliderStyle(handle_color='Blue'))"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["s1 = widgets.IntSlider(description='My Handle')\n", "s1.style.handle_color = 'Blue'\n", "s1"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}