{"cells": [{"cell_type": "markdown", "id": "f894b5db", "metadata": {}, "source": ["# Task: Implement Queue using Doubly Linked List in Python\n", "\n", "## Problem Statement:\n", "Design a Queue data structure using a **Doubly Linked List**, which follows the **First In First Out (FIFO)** principle. Implement all standard queue operations efficiently.\n", "\n", "## Steps:\n", "\n", "1. **Create Node Class** with `data`, `prev`, and `next` pointers.\n", "2. **Initialize Queue** with `head`, `tail`, and `size` attributes.\n", "3. **enqueue()**:\n", "   - Add new node at the `tail` of the queue.\n", "   - Update pointers accordingly.\n", "4. **dequeue()**:\n", "   - Remove node from the `head` of the queue.\n", "   - Return the removed node’s data.\n", "5. **first()**:\n", "   - Return data at `head` without removing it.\n", "6. **size()**:\n", "   - Return the current size of the queue.\n", "7. **isEmpty()**:\n", "   - Return `True` if size is 0, else `False`.\n", "8. **printqueue()**:\n", "   - Traverse from head to tail and print all elements.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8ed8fe13", "metadata": {}, "outputs": [], "source": ["class Node:\n", "\n", "    def __init__(self,data):\n", "        self.data = data\n", "        self.next = None\n", "        self.prev = None"]}, {"cell_type": "code", "execution_count": 2, "id": "080dbf93", "metadata": {}, "outputs": [], "source": ["class Queue:\n", "    def __init__(self):\n", "        self.head = None\n", "        self.last = None\n", "    \n", "    def enqueue(self,data):\n", "        if self.last is None:\n", "            self.head = Node(data)\n", "            self.last = self.head\n", "        else:\n", "            self.last.next = Node(data)\n", "            self.last.next.prev = self.last\n", "            self.last = self.last.next\n", "    \n", "    def dequeue(self):\n", "        if self.head is None:\n", "            return None\n", "        else:\n", "            temp = self.head.data\n", "            self.head = self.head.next\n", "            self.head.prev = None\n", "            return temp\n", "        \n", "    def first(self):\n", "        return  self.head.data\n", "    \n", "    def size(self):\n", "        temp = self.head\n", "        count = 0\n", "        while temp is not None:\n", "            count += 1\n", "            temp = temp.next\n", "        return count\n", "    \n", "    def is<PERSON><PERSON>y(self):\n", "        if self.head is None:\n", "            return True\n", "        else:\n", "            return False\n", "        \n", "    def printqueue(self):\n", "        print(\"queue elements are:\")\n", "        temp = self.head\n", "        while temp is not None:\n", "            print(temp.data,end=\"=>\")\n", "            temp = temp.next"]}, {"cell_type": "code", "execution_count": 3, "id": "0137b9ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Queue operations using doubly linked list\n", "queue elements are:\n", "4=>5=>6=>7=>\n", "first element is 4\n", "Size of the queue is 4\n", "After applying dequeue() two times\n", "queue elements are:\n", "6=>7=>\n", "queue is empty: False\n"]}], "source": ["if __name__ == '__main__':\n", "    queue = Queue()\n", "    print(\"Queue operations using doubly linked list\")\n", "    queue.enqueue(4)\n", "    queue.enqueue(5)\n", "    queue.enqueue(6)\n", "    queue.enqueue(7)\n", "    queue.printqueue()\n", "    print(\"\\nfirst element is\", queue.first())\n", "    print(\"Size of the queue is\", queue.size())\n", "    queue.dequeue()\n", "    queue.dequeue()\n", "    print(\"After applying dequeue() two times\")\n", "    queue.printqueue()\n", "    print(\"\\nqueue is empty:\", queue.isEmpty())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}