{"cells": [{"cell_type": "markdown", "id": "14f904f4", "metadata": {}, "source": ["# Task: Multithreaded Priority Queue in Python\n", "\n", "## Problem Statement:\n", "Implement a **multithreaded priority queue** using Python. Use the `queue.PriorityQueue` module to manage tasks among multiple threads. In a priority queue, elements with **higher priority are dequeued first**. If multiple elements have the same priority, they are processed in the order they were added.\n", "\n", "## Steps:\n", "1. **Import required modules**: `threading`, `queue`, and `time`.\n", "2. **Create a PriorityQueue** instance.\n", "3. **Define a worker thread function** that retrieves items using `get()` and processes them.\n", "4. **Spawn multiple threads** and start them.\n", "5. **Insert tasks into the queue** using `put()`, with each item as a tuple `(priority, task)`.\n", "6. **Wait for the queue to be empty**, or use `join()` to block until all tasks are done.\n", "7. Optionally, use `qsize()`, `empty()`, and `full()` to monitor queue state.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3ed4ee35", "metadata": {}, "outputs": [], "source": ["import queue\n", "import threading\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "id": "c7dd266c", "metadata": {}, "outputs": [], "source": ["thread_exit_Flag = 0"]}, {"cell_type": "code", "execution_count": 3, "id": "22934f3c", "metadata": {}, "outputs": [], "source": ["class sample_Thread(threading.Thread):\n", "    def __init__(self, threadID, name, q):\n", "        threading.Thread.__init__(self)\n", "        self.threadID = threadID\n", "        self.name = name\n", "        self.q = q\n", "\n", "    def run(self):\n", "        print(\"initializing \" + self.name)\n", "        process_data(self.name, self.q)\n", "        print(\"Exiting \" + self.name)"]}, {"cell_type": "code", "execution_count": 4, "id": "b77cb6b1", "metadata": {}, "outputs": [], "source": ["def process_data(thread<PERSON><PERSON>, q):\n", "    while not thread_exit_Flag:\n", "        queueLock.acquire()\n", "        if not workQueue.empty():\n", "            data = q.get()\n", "            queueLock.release()\n", "            print(\"%s processing %s\" % (threadName, data))\n", "        else:\n", "            queueLock.release()\n", "            time.sleep(1)"]}, {"cell_type": "code", "execution_count": 5, "id": "8a516815", "metadata": {}, "outputs": [], "source": ["thread_list = [\"Thread-1\", \"Thread-2\", \"Thread-3\"]\n", "name_list = [\"A\", \"B\", \"C\", \"D\", \"E\"]\n", "queueLock = threading.Lock()\n", "workQueue = queue.Queue(10)\n", "threads = []\n", "threadID = 1"]}, {"cell_type": "code", "execution_count": 6, "id": "43760c6e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["initializing Thread-1\n", "initializing Thread-2\n", "initializing Thread-3\n"]}], "source": ["for thread_name in thread_list:\n", "    thread = sample_Thread(threadID, thread_name, workQueue)\n", "    thread.start()\n", "    threads.append(thread)\n", "    threadID += 1"]}, {"cell_type": "code", "execution_count": 7, "id": "f01270dc", "metadata": {}, "outputs": [], "source": ["queueLock.acquire()\n", "for item in name_list:\n", "    workQueue.put(item)\n", "queueLock.release()"]}, {"cell_type": "code", "execution_count": 8, "id": "a8dfcb2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread-2 processing AThread-3 processing B\n", "Thread-3 processing C\n", "Thread-3 processing D\n", "Thread-3 processing E\n", "\n"]}], "source": ["while not workQueue.empty():\n", "    pass"]}, {"cell_type": "code", "execution_count": 9, "id": "030eccc6", "metadata": {}, "outputs": [], "source": ["thread_exit_Flag = 1"]}, {"cell_type": "code", "execution_count": 10, "id": "5db12c90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exiting Thread-1Exiting Thread-3\n", "Exiting Thread-2\n", "\n"]}], "source": ["for t in threads:\n", "    t.join()"]}, {"cell_type": "code", "execution_count": 11, "id": "366eba94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exit Main Thread\n"]}], "source": ["print(\"Exit Main Thread\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}