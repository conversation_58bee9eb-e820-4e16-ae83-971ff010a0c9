{"cells": [{"cell_type": "markdown", "id": "d9680a67", "metadata": {}, "source": ["# Task: Sum of Nested Lists Using Recursion\n", "\n", "## Problem Statement:\n", "Write a Python program that calculates the **sum of all numbers** in a **nested list** using recursion. A nested list may contain integers or other lists which can also be nested.\n", "\n", "## Steps:\n", "1. Define a **recursive function** that takes a list as input.\n", "2. Initialize a variable to store the cumulative sum.\n", "3. Iterate through each element in the list:\n", "   - If the element is an integer, add it to the sum.\n", "   - If the element is a list, **recursively call the function** on that sublist and add the result to the sum.\n", "4. Return the final sum after traversing the entire list.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "be610bf7", "metadata": {}, "outputs": [], "source": ["def sum_nested_list(nested_list):\n", "    total = 0\n", "    for item in nested_list:\n", "        if isinstance(item, list):\n", "            total += sum_nested_list(item)\n", "        else:\n", "            total += item\n", "    return total"]}, {"cell_type": "code", "execution_count": 2, "id": "79992745", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["test = [1, 2, [3,4], [5,6]]\n", "print(sum_nested_list(test))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}