{"cells": [{"cell_type": "markdown", "id": "4f663187", "metadata": {}, "source": ["# Task: Swapping Successive Permutations\n", "\n", "## Problem Statement:\n", "Write a Python program to generate all **permutations** of `n` items such that **each successive permutation differs from the previous one by swapping any two items**. This ensures minimal change between permutations.\n", "\n", "## Steps:\n", "1. Use **He<PERSON>’s Algorithm**, which is designed to generate permutations with minimal changes (swapping two elements at a time).\n", "2. Define a **recursive function** that yields permutations by swapping.\n", "3. **Print or return** each generated permutation in sequence.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4282718d", "metadata": {}, "outputs": [], "source": ["from operator import itemgetter"]}, {"cell_type": "code", "execution_count": 2, "id": "7f41b047", "metadata": {}, "outputs": [], "source": ["DEBUG = False"]}, {"cell_type": "code", "execution_count": 3, "id": "977e97f3", "metadata": {}, "outputs": [], "source": ["def spermutations(n):\n", "    sign = 1\n", "    p = [[i, 0 if i == 0 else -1] for i in range(n)]\n", "\n", "    if DEBUG: print(' #', p)\n", "    yield tuple(pp[0] for pp in p), sign\n", "\n", "    while any(pp[1] for pp in p):\n", "        i1, (n1, d1) = max(((i, pp) for i, pp in enumerate(p) if pp[1]), key=itemgetter(1))\n", "        sign *= -1\n", "        if d1 == -1:\n", "            i2 = i1 - 1\n", "            p[i1], p[i2] = p[i2], p[i1]\n", "            if i2 == 0 or p[i2 - 1][0] > n1:\n", "                p[i2][1] = 0\n", "        elif d1 == 1:\n", "            i2 = i1 + 1\n", "            p[i1], p[i2] = p[i2], p[i1]\n", "            if i2 == n - 1 or p[i2 + 1][0] > n1:\n", "                p[i2][1] = 0\n", "        if DEBUG: print(' #', p)\n", "        yield tuple(pp[0] for pp in p), sign\n", "\n", "        for i3, pp in enumerate(p):\n", "            n3, d3 = pp\n", "            if n3 > n1:\n", "                pp[1] = 1 if i3 < i2 else -1\n", "                if DEBUG: print(' # Set Moving')"]}, {"cell_type": "code", "execution_count": 4, "id": "f720ac7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Permutations and sign of 3 items\n", "Permutation: (0, 1, 2) Sign:  1\n", "Permutation: (0, 2, 1) Sign: -1\n", "Permutation: (2, 0, 1) Sign:  1\n", "Permutation: (2, 1, 0) Sign: -1\n", "Permutation: (1, 2, 0) Sign:  1\n", "Permutation: (1, 0, 2) Sign: -1\n", "\n", "Permutations and sign of 4 items\n", "Permutation: (0, 1, 2, 3) Sign:  1\n", "Permutation: (0, 1, 3, 2) Sign: -1\n", "Permutation: (0, 3, 1, 2) Sign:  1\n", "Permutation: (3, 0, 1, 2) Sign: -1\n", "Permutation: (3, 0, 2, 1) Sign:  1\n", "Permutation: (0, 3, 2, 1) Sign: -1\n", "Permutation: (0, 2, 3, 1) Sign:  1\n", "Permutation: (0, 2, 1, 3) Sign: -1\n", "Permutation: (2, 0, 1, 3) Sign:  1\n", "Permutation: (2, 0, 3, 1) Sign: -1\n", "Permutation: (2, 3, 0, 1) Sign:  1\n", "Permutation: (3, 2, 0, 1) Sign: -1\n", "Permutation: (3, 2, 1, 0) Sign:  1\n", "Permutation: (2, 3, 1, 0) Sign: -1\n", "Permutation: (2, 1, 3, 0) Sign:  1\n", "Permutation: (2, 1, 0, 3) Sign: -1\n", "Permutation: (1, 2, 0, 3) Sign:  1\n", "Permutation: (1, 2, 3, 0) Sign: -1\n", "Permutation: (1, 3, 2, 0) Sign:  1\n", "Permutation: (3, 1, 2, 0) Sign: -1\n", "Permutation: (3, 1, 0, 2) Sign:  1\n", "Permutation: (1, 3, 0, 2) Sign: -1\n", "Permutation: (1, 0, 3, 2) Sign:  1\n", "Permutation: (1, 0, 2, 3) Sign: -1\n"]}], "source": ["if __name__ == '__main__':\n", "    from itertools import permutations\n", "\n", "    for n in (3, 4):\n", "        print('\\nPermutations and sign of %i items' % n)\n", "        sp = set()\n", "        for i in spermutations(n):\n", "            sp.add(i[0])\n", "            print('Permutation: %r Sign: %2i' % i)\n", "        p = set(permutations(range(n)))\n", "        assert sp == p, 'Two methods of generating permutations do not agree'"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}