{"cells": [{"cell_type": "markdown", "id": "568f70ee", "metadata": {}, "source": ["# Task: Program to Reverse a Linked List Using Stack\n", "\n", "## Problem Statement:\n", "Given a singly linked list, reverse the order of its elements using an auxiliary stack. This method leverages the LIFO (Last-In-First-Out) nature of stacks to reverse the list.\n", "\n", "## Steps:\n", "1. Traverse the linked list and push all its node references onto a stack.\n", "2. Reset the pointer to the head of the list.\n", "3. Pop elements from the stack one by one and reassign the data of each node in the list.\n", "4. The list is now reversed in place.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "165af46f", "metadata": {}, "outputs": [], "source": ["class Node:\n", "\n", "    def __init__(self,data,next):\n", "        self.data = data\n", "        self.next = next"]}, {"cell_type": "code", "execution_count": 2, "id": "f9832732", "metadata": {}, "outputs": [], "source": ["class LinkedList:\n", "    def __init__(self):\n", "        self.head = None\n", "\n", "    def push(self, new_data): \n", "        new_node = Node(new_data, self.head) \n", "        self.head = new_node\n", "\n", "    def reverseList(self): \n", "        stk = []\n", "        ptr = self.head \n", "        while ptr.next != None: \n", "            stk.append(ptr) \n", "            ptr = ptr.next\n", "        self.head = ptr \n", "        while len(stk) != 0: \n", "            ptr.next = stk.pop() \n", "            ptr = ptr.next\n", "        ptr.next = None\n", "\n", "    def printList(self):\n", "        curr = self.head\n", "        while curr: \n", "            print(curr.data, end = \" \") \n", "            curr = curr.next"]}, {"cell_type": "code", "execution_count": 3, "id": "15014599", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5 4 3 2 1 "]}], "source": ["if __name__ == \"__main__\":\n", "    linkedList = LinkedList() \n", "    linkedList.push(5) \n", "    linkedList.push(4) \n", "    linkedList.push(3) \n", "    linkedList.push(2) \n", "    linkedList.push(1) \n", "    linkedList.reverseList() \n", "    linkedList.printList()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}