{"cells": [{"cell_type": "markdown", "id": "9b71d2c3-4291-4da5-95fd-595cf283ca08", "metadata": {}, "source": ["# Task: Python Program to Find the Type of IP Address Using Regex\n", "\n", "## Problem Statement:\n", "Given an IP address as input, write a Python program to identify whether it is of type IPv4, IPv6, or Neither using Regular Expressions.\n", "\n", "### Steps:\n", "\n", "1. Take the IP address as input.\n", "2. Use a regular expression to check if the input matches the pattern for a valid IPv4 address:\n", "   - IPv4 format: Four decimal numbers (0–255) separated by dots.\n", "3. If the input matches the IPv4 pattern, print \"IPv4\".\n", "4. If not, check if the input matches the pattern for a valid IPv6 address using another regex:\n", "   - IPv6 format: Eight groups of four hexadecimal digits separated by colons.\n", "5. If the input matches the IPv6 pattern, print \"IPv6\".\n", "6. If the input does not match either pattern, print \"Neither\".\n"]}, {"cell_type": "code", "execution_count": 1, "id": "735e23b6-44e3-41d2-87d2-a3f4a08d750d", "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 2, "id": "b0dc873a-1218-438f-b150-0bdaf214f1b5", "metadata": {}, "outputs": [], "source": ["ipv4 = '''^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\\.( \n", "            25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\\.( \n", "            25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\\.( \n", "            25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$'''"]}, {"cell_type": "code", "execution_count": 3, "id": "a6746754-6d8a-4d31-bb74-0cdb23b06a53", "metadata": {}, "outputs": [], "source": ["ipv6 = '''(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|\n", "        ([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:)\n", "        {1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1\n", "        ,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}\n", "        :){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{\n", "        1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA\n", "        -F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a\n", "        -fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0\n", "        -9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,\n", "        4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}\n", "        :){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9\n", "        ])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0\n", "        -9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]\n", "        |1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]\n", "        |1{0,1}[0-9]){0,1}[0-9]))'''"]}, {"cell_type": "code", "execution_count": 4, "id": "d173bfb9-5370-425f-98d8-11115d9d8a66", "metadata": {}, "outputs": [], "source": ["def find(Ip):  \n", "    if re.search(ipv4, Ip):\n", "        print(\"IPv4\")\n", "    elif re.search(ipv6, Ip):\n", "        print(\"IPv6\")\n", "    else:\n", "        print(\"Neither\")"]}, {"cell_type": "code", "execution_count": 5, "id": "cb9d5cd8-9dda-4a9b-b8ce-aafbdbd52ba1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IPv4\n", "IPv6\n", "Neither\n"]}], "source": ["if __name__ == '__main__' :  \n", "      \n", "    Ip = \"***********\"\n", "        \n", "    find(Ip) \n", "  \n", "    Ip = \"3001:0da8:75a3:0000:0000:8a2e:0370:7334\"\n", "    find(Ip) \n", "  \n", "    Ip = \"***********.52\"\n", "    find(Ip)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}