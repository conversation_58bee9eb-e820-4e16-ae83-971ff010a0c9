{"cells": [{"cell_type": "markdown", "id": "8ddfbfce", "metadata": {}, "source": ["# Task: Extract a Single Value from JSON Response\n", "\n", "## Problem Statement:\n", "Given a JSON response from an API or file, write a Python program to extract the value associated with a specific key.\n", "\n", "## Steps:\n", "1. **Import the `json` module** (if reading from a JSON string or file).\n", "2. **Load or parse** the JSON data using `json.loads()` (for strings) or `json.load()` (for files).\n", "3. **Access the desired value** using key/index lookups depending on the structure of the JSON.\n", "4. **Print or store** the extracted value.\n"]}, {"cell_type": "code", "execution_count": null, "id": "dbecc0c6", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "105d1385", "metadata": {}, "outputs": [], "source": ["base_url = \"https://v6.exchangerate-api.com/v6/************************/pair/\""]}, {"cell_type": "code", "execution_count": null, "id": "d4a28143", "metadata": {}, "outputs": [], "source": ["First = input(\"Enter First Currency Value\")\n", "Second = input(\"Enter  Second Currency Value\")"]}, {"cell_type": "code", "execution_count": null, "id": "34de0c28", "metadata": {}, "outputs": [], "source": ["result = First+\"/\"+Second\n", "final_url = base_url+result"]}, {"cell_type": "code", "execution_count": null, "id": "2141eda4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Network or API error: 404 Client Error: Not Found for url: https://v6.exchangerate-api.com/v6/************************/pair//\n"]}], "source": ["try:\n", "    response = requests.get(final_url)\n", "    response.raise_for_status()\n", "    json_data = response.json()\n", "\n", "    print(\"\\nFull JSON Response:\")\n", "    print(json.dumps(json_data, indent=4))\n", "\n", "    if json_data['result'] == 'success':\n", "        conversion_rate = json_data['conversion_rate']\n", "        print(f\"\\nConversion rate from {First} to {Second} = {conversion_rate}\")\n", "    else:\n", "        print(\"\\nError in API response:\", json_data.get('error-type', 'Unknown error'))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(\"\\nNetwork or API error:\", e)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}