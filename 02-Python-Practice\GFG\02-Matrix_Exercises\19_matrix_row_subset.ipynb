{"cells": [{"cell_type": "markdown", "id": "25ee701e-f337-4e41-849e-208096346b14", "metadata": {}, "source": ["# Task: Matrix Row Subset\n", "\n", "## Problem Statement:\n", "Given two matrices, extract all rows from the first matrix that are a subset of any row in the second matrix.\n", "\n", "### Steps:\n", "1. Iterate through each row in the first matrix.\n", "2. For each row, iterate through all rows in the second matrix.\n", "3. Convert both rows to sets and check if the first is a subset of the second using set intersection or `issubset()`.\n", "4. If a match is found, add the row to the result.\n", "5. Return all rows from the first matrix that are subsets of any row in the second matrix.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "47cb4ab7-760b-4f25-9a7a-3bf0544dffbe", "metadata": {}, "outputs": [], "source": ["def find_matrix_row_subsets(test_matrix, check_matrix):\n", "    res = []\n", "    for row in check_matrix:\n", "        for lst in test_matrix:\n", "            if set(row).issubset(set(lst)):\n", "                res.append(row)\n", "    return res"]}, {"cell_type": "code", "execution_count": 2, "id": "176c39e4-341f-4975-9138-b01eaad31d51", "metadata": {}, "outputs": [], "source": ["test_list = [[4, 5, 7], [2, 3, 4], [9, 8, 0]]"]}, {"cell_type": "code", "execution_count": 3, "id": "90a17b0c-cfb7-41d5-a1b5-4305d6e907bb", "metadata": {}, "outputs": [], "source": ["check_matr = [[2, 3], [1, 2], [9, 0]]"]}, {"cell_type": "code", "execution_count": 4, "id": "b1f95233-90ff-44e4-ba34-e5aa8296d48c", "metadata": {}, "outputs": [], "source": ["result = find_matrix_row_subsets(test_list, check_matr)"]}, {"cell_type": "code", "execution_count": 5, "id": "de3679a4-7512-4320-a3e4-cbaa1c5924ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matrix row subsets: [[2, 3], [9, 0]]\n"]}], "source": ["print(\"Matrix row subsets:\", result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}