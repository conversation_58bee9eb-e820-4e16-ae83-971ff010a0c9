{"cells": [{"cell_type": "markdown", "id": "cb66d44e", "metadata": {}, "source": ["# Task: Password Validator\n", "\n", "## Problem Statement:\n", "Write a Python program to **validate a user's password** based on the following criteria using boolean expressions:\n", "- Minimum length (e.g., at least 8 characters)\n", "- Contains at least one digit\n", "- Contains at least one special character (e.g., !@#$%^&*)\n", "- Contains at least one uppercase and one lowercase letter\n", "\n", "## Steps:\n", "1. **Prompt the user** to input a password.\n", "2. Use **boolean expressions** to check if the password:\n", "   - Has the minimum required length\n", "   - Contains at least one digit\n", "   - Contains at least one special character\n", "   - Contains both uppercase and lowercase letters\n", "3. **Print a message** indicating whether the password is valid or specify which criteria failed.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2d502d7f", "metadata": {}, "outputs": [], "source": ["def Is_password_valid(password):\n", "    if len(password) < 8:\n", "        return False\n", "    \n", "    password_has_digit = any(char.isdigit() for char in password)\n", "    password_special_characters = \"!@#$%^&*()_+[]{}|;:,.<>?/\"\n", "    has_special_character = any(char in password_special_characters for char in password)\n", "    password_has_uppercase = any(char.isupper() for char in password)\n", "    password_has_lowercase = any(char.islower() for char in password)\n", "    \n", "    return password_has_digit and has_special_character and password_has_uppercase and password_has_lowercase"]}, {"cell_type": "code", "execution_count": 2, "id": "fdb36cd9", "metadata": {}, "outputs": [], "source": ["def main():\n", "    try:\n", "        password = input(\"Input your password: \")\n", "        if Is_password_valid(password):\n", "            print(\"Password is valid.\")\n", "        else:\n", "            print(\"Password is invalid.\")\n", "    except Exception as e:\n", "        print(\"An error occurred:\", e)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d3064795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Password is valid.\n"]}], "source": ["if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}