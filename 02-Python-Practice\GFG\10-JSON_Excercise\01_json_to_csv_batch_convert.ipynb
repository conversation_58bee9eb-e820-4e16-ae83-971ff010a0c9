{"cells": [{"cell_type": "markdown", "id": "780de395", "metadata": {}, "source": ["# Task: Convert Multiple JSON Files to CSV\n", "\n", "## Problem Statement:\n", "Write a Python program that reads multiple JSON files from a directory, merges their content using pandas, and exports the combined data into a single CSV file.\n", "\n", "## Steps:\n", "1. **Load the JSON files with the help of pandas DataFrame** using `pd.read_json()` for each file.\n", "2. **Concatenate the DataFrames** into one unified DataFrame using `pd.concat()`.\n", "3. **Convert the concatenated DataFrame into a CSV file** using `to_csv()` method.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "7463da41", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "markdown", "id": "7c20fb45", "metadata": {}, "source": ["## If all columns match"]}, {"cell_type": "code", "execution_count": 2, "id": "8cc42766", "metadata": {}, "outputs": [], "source": ["df1 = pd.read_json('file1.json')\n", "df2 = pd.read_json('file2.json')"]}, {"cell_type": "code", "execution_count": 3, "id": "02b3b4df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data from file1.json\n", "   ID    Name  Marks Grade\n", "0  23     Ram     89     B\n", "1  43    Deep     97     A\n", "2  12    Yash     45     F\n", "3  13    <PERSON><PERSON>     78     C\n", "4  67   <PERSON><PERSON><PERSON>     56     E\n", "5  89  Aditya     76     C\n", "\n", "Data from file2.json:\n", "   ID     Name  Marks Grade\n", "0  90    Akash     81     B\n", "1  56  Chalsea     87     B\n", "2  34    Divya    100     A\n", "3  96    Sajal     89     B\n", "4  45  <PERSON><PERSON><PERSON>     78     C\n"]}], "source": ["print(\"Data from file1.json\")\n", "print(df1)\n", "print(\"\\nData from file2.json:\")\n", "print(df2)"]}, {"cell_type": "code", "execution_count": 4, "id": "79825fa8", "metadata": {}, "outputs": [], "source": ["df_combined = pd.concat([df1, df2], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "703b565e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Combined DataFrame:\n", "    ID     Name  Marks Grade\n", "0   23      Ram     89     B\n", "1   43     Deep     97     A\n", "2   12     Yash     45     F\n", "3   13     <PERSON><PERSON>     78     C\n", "4   67    <PERSON><PERSON><PERSON>     56     E\n", "5   89   Aditya     76     C\n", "6   90    Akash     81     B\n", "7   56  Chalsea     87     B\n", "8   34    Divya    100     A\n", "9   96    Sajal     89     B\n", "10  45  <PERSON><PERSON><PERSON>     78     C\n"]}], "source": ["print(\"\\nCombined DataFrame:\")\n", "print(df_combined)"]}, {"cell_type": "code", "execution_count": 6, "id": "7ce06393", "metadata": {}, "outputs": [], "source": ["df_combined.to_csv(\"CSV.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 7, "id": "307f4811", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Data loaded from CSV.csv:\n", "    ID     Name  Marks Grade\n", "0   23      Ram     89     B\n", "1   43     Deep     97     A\n", "2   12     Yash     45     F\n", "3   13     <PERSON><PERSON>     78     C\n", "4   67    <PERSON><PERSON><PERSON>     56     E\n", "5   89   Aditya     76     C\n", "6   90    Akash     81     B\n", "7   56  Chalsea     87     B\n", "8   34    Divya    100     A\n", "9   96    Sajal     89     B\n", "10  45  <PERSON><PERSON><PERSON>     78     C\n"]}], "source": ["print(\"\\nData loaded from CSV.csv:\")\n", "result = pd.read_csv(\"CSV.csv\")\n", "print(result)"]}, {"cell_type": "markdown", "id": "f13798b7", "metadata": {}, "source": ["## If some columns match"]}, {"cell_type": "code", "execution_count": 8, "id": "12f8245e", "metadata": {}, "outputs": [], "source": ["df3 = pd.read_json('file3.json')\n", "df4 = pd.read_json('file4.json')"]}, {"cell_type": "code", "execution_count": 9, "id": "30808352", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data from file3.json:\n", "   ID    Name  Marks\n", "0  23     Ram     89\n", "1  43    Deep     97\n", "2  12    <PERSON><PERSON>     45\n", "3  13    <PERSON><PERSON>     78\n", "4  67   <PERSON><PERSON><PERSON>     56\n", "5  89  <PERSON><PERSON><PERSON>     76 \n", "\n"]}], "source": ["print(\"Data from file3.json:\")\n", "print(df3, \"\\n\")"]}, {"cell_type": "code", "execution_count": 10, "id": "508f193f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data from file4.json:\n", "   ID    Name Grade\n", "0  23     Ram     B\n", "1  43    Deep     A\n", "2  12    Yash     F\n", "3  67   <PERSON><PERSON><PERSON>\n", "4  89  Aditya     C \n", "\n"]}], "source": ["print(\"Data from file4.json:\")\n", "print(df4, \"\\n\")"]}, {"cell_type": "code", "execution_count": 11, "id": "b1948e8c", "metadata": {}, "outputs": [], "source": ["df_inner = pd.merge(df3, df4, how='inner', on=['ID', 'Name'])\n", "df_outer = pd.merge(df3, df4, how='outer', on=['ID', 'Name'])\n", "df_left = pd.merge(df3, df4, how='left', on=['ID', 'Name'])\n", "df_right = pd.merge(df3, df4, how='right', on=['ID', 'Name'])"]}, {"cell_type": "code", "execution_count": 12, "id": "a8615ef8", "metadata": {}, "outputs": [], "source": ["df_inner.to_csv(\"CSV_inner.csv\", index=False)\n", "df_outer.to_csv(\"CSV_outer.csv\", index=False)\n", "df_left.to_csv(\"CSV_left.csv\", index=False)\n", "df_right.to_csv(\"CSV_right.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "3253d941", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Outer Join Result:\n", "    ID    Name  Marks Grade\n", "0  12    Yash     45     F\n", "1  13    Aman     78   NaN\n", "2  23     Ram     89     B\n", "3  43    Deep     97     A\n", "4  67   <PERSON><PERSON><PERSON>     56     E\n", "5  89  Aditya     76     C \n", "\n", "Inner Join Result:\n", "    ID    Name  Marks Grade\n", "0  23     Ram     89     B\n", "1  43    Deep     97     A\n", "2  12    Yash     45     F\n", "3  67   <PERSON><PERSON><PERSON>     56     E\n", "4  89  Aditya     76     C \n", "\n", "Left Join Result:\n", "    ID    Name  Marks Grade\n", "0  23     Ram     89     B\n", "1  43    Deep     97     A\n", "2  12    Yash     45     F\n", "3  13    Aman     78   NaN\n", "4  67   <PERSON><PERSON><PERSON>     56     E\n", "5  89  Aditya     76     C \n", "\n", "Right Join Result:\n", "    ID    Name  Marks Grade\n", "0  23     Ram     89     B\n", "1  43    Deep     97     A\n", "2  12    Yash     45     F\n", "3  67   <PERSON><PERSON><PERSON>     56     E\n", "4  89  Aditya     76     C \n", "\n"]}], "source": ["print(\"Outer Join Result:\\n\", pd.read_csv(\"CSV_outer.csv\"), \"\\n\")\n", "print(\"Inner Join Result:\\n\", pd.read_csv(\"CSV_inner.csv\"), \"\\n\")\n", "print(\"Left Join Result:\\n\", pd.read_csv(\"CSV_left.csv\"), \"\\n\")\n", "print(\"Right Join Result:\\n\", pd.read_csv(\"CSV_right.csv\"), \"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}