{"cells": [{"cell_type": "markdown", "id": "4db0f5fb", "metadata": {}, "source": ["# Task: Read content from one file and write it into another file\n", "\n", "## Problem Statement:\n", "Given a source file, the task is to **read all the contents** of this file and **write them to a new file** (target file), effectively duplicating the content.\n", "\n", "## Steps:\n", "1. Open the source file in read mode using `open('source.txt', 'r')`.\n", "2. Open or create the destination file in write mode using `open('destination.txt', 'w')`.\n", "3. Read the entire content from the source file using `.read()` or line by line using `.readlines()`.\n", "4. Write the content into the destination file using `.write()` or `.writelines()`.\n", "5. Close both files using `.close()` or use a `with` statement to handle files automatically.\n"]}, {"cell_type": "markdown", "id": "f54b3244", "metadata": {}, "source": ["## Method 1: Using loops"]}, {"cell_type": "code", "execution_count": 1, "id": "1a23008d", "metadata": {}, "outputs": [], "source": ["with open(\"input.txt\", \"r\") as input:\n", "\n", "    with open(\"output.txt\", \"w\") as output:\n", "\n", "        for line in input:\n", "            output.write(line)"]}, {"cell_type": "markdown", "id": "9bbe9cd8", "metadata": {}, "source": ["## Method 2: Using File methods"]}, {"cell_type": "code", "execution_count": 2, "id": "b0e72444", "metadata": {}, "outputs": [], "source": ["output_file = open(\"output.txt\", \"w\")\n", "\n", "with open(\"input.txt\", \"r\") as scan:\n", "    output_file.write(scan.read())\n", "\n", "output_file.close()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}