{"cells": [{"cell_type": "markdown", "id": "7655504c-6667-46a4-9fbe-01913e9196d6", "metadata": {}, "source": ["# Task: Convert Matrix to Dictionary\n", "\n", "## Problem Statement:\n", "Given a matrix (2D list), convert it into a dictionary where each key is the row number (starting from 1) and the corresponding value is the row itself.\n", "\n", "### Steps:\n", "1. Use `enumerate` to iterate over the matrix rows along with their indices.\n", "2. For each row, assign the index (offset by 1) as the key and the row as the value.\n", "3. Construct the dictionary using a dictionary comprehension or loop.\n", "4. Return the resulting dictionary.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "794fbd7d-df06-4222-92e3-8a6f09e9bb6b", "metadata": {}, "outputs": [], "source": ["li = [[5, 6, 7], [8, 3, 2], [8, 2, 1]]"]}, {"cell_type": "code", "execution_count": 2, "id": "3bb9acb4-f243-43b4-912a-7653885d84b8", "metadata": {}, "outputs": [], "source": ["res = {idx + 1: val for idx, val in enumerate(li)}"]}, {"cell_type": "code", "execution_count": 3, "id": "1709511f-6e02-4c1e-92d5-91eebe209149", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1: [5, 6, 7], 2: [8, 3, 2], 3: [8, 2, 1]}\n"]}], "source": ["print(res)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}