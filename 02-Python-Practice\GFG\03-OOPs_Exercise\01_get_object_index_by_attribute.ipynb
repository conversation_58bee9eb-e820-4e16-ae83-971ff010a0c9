{"cells": [{"cell_type": "markdown", "id": "1ca29981-bf77-43ab-8dda-0fd7f323cb2a", "metadata": {}, "source": ["# Task: Get Index in the List of Objects by Attribute in Python\n", "\n", "## Problem Statement:\n", "Given a list of objects, find the index of the first object that matches a specific attribute value using Python.\n", "\n", "### Steps:\n", "1. Use the `enumerate()` function to loop through the list while tracking index and object.\n", "2. For each object, check if the desired attribute matches the target value.\n", "3. If a match is found, return the corresponding index.\n", "4. If no match is found after the loop, handle it appropriately (e.g., return -1 or raise an exception).\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0f6d5490-cf58-4ccb-b04f-594847cc8cca", "metadata": {}, "outputs": [], "source": ["class X:\n", "    def __init__(self,val):\n", "        self.val = val"]}, {"cell_type": "code", "execution_count": 2, "id": "aff5ee1a-4275-4f71-83bf-f8d44282daea", "metadata": {}, "outputs": [], "source": ["def getIndex(li,target):\n", "    for index, x in enumerate(li):\n", "        if x.val == target:\n", "            return index\n", "    return -1"]}, {"cell_type": "code", "execution_count": 3, "id": "7b2f59bd-31a0-4837-bf46-43a28443ae49", "metadata": {}, "outputs": [], "source": ["li = [1,2,3,4,5,6]"]}, {"cell_type": "code", "execution_count": 4, "id": "f63089c4-a0e6-4329-bae2-4bc46d4c2535", "metadata": {}, "outputs": [], "source": ["a = [X(i) for i in li]"]}, {"cell_type": "code", "execution_count": 5, "id": "8367fdea-c36d-4822-bfb8-fb790a098cef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["print(getIndex(a,3))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}