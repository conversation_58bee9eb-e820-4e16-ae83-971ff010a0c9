{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Errors and Exceptions Homework"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 1\n", "Handle the exception thrown by the code below by using <code>try</code> and <code>except</code> blocks."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Something Wrong!, You should check list's memebers data type and applied operation\n"]}], "source": ["try:\n", "    for i in ['a','b','c']:\n", "        print(i**2)\n", "except:\n", "    print(\"Something Wrong!, You should check list's memebers data type and applied operation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 2\n", "Handle the exception thrown by the code below by using <code>try</code> and <code>except</code> blocks. Then use a <code>finally</code> block to print 'All Done.'"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Denominator can not be zero!!\n"]}], "source": ["x = 5\n", "y = 0\n", "\n", "try:\n", "    z = x/y\n", "except ZeroDivisionError:\n", "    print(\"Denominator can not be zero!!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 3\n", "Write a function that asks for an integer and prints the square of it. Use a <code>while</code> loop with a <code>try</code>, <code>except</code>, <code>else</code> block to account for incorrect inputs."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def ask():\n", "    while True:\n", "        try:\n", "            a = int(input(\"Input an integer: \"))\n", "        except:\n", "            print(\"An error occurred! Please try again!\")\n", "            continue\n", "        else:\n", "            print(\"Thank you, your number squared is: \" + str(a))\n", "            break"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["Input an integer:  null\n"]}, {"name": "stdout", "output_type": "stream", "text": ["An error occurred! Please try again!\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Input an integer:  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Thank you, your number squared is: 2\n"]}], "source": ["ask()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Great Job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}