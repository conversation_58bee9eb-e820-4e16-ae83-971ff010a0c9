{"cells": [{"cell_type": "markdown", "id": "0b2720d8", "metadata": {}, "source": ["# Task: Anagram Substring Search in Python\n", "\n", "## Problem Statement:\n", "Given a string `txt` and a pattern `pat`, find all starting indices where any **anagram** (i.e. permutation) of `pat` occurs in `txt`. The expected time complexity is O(n), where n is the length of `txt`.\n", "\n", "## Steps:\n", "1. Use a **sliding window** of size equal to the length of `pat` to traverse the string `txt`.\n", "2. Maintain **character frequency counters** for both `pat` and the current window in `txt`.\n", "3. For each window:\n", "   - If the frequency of characters in the window matches that of `pat`, record the starting index.\n", "4. Use `collections.Counter` or a fixed-size list for character counting to optimize comparisons.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "98d04f21", "metadata": {}, "outputs": [], "source": ["MAX = 256"]}, {"cell_type": "code", "execution_count": 2, "id": "c179e823", "metadata": {}, "outputs": [], "source": ["def compare(arr1, arr2):\n", "    for i in range(MAX):\n", "        if arr1[i] != arr2[i]:\n", "            return False\n", "    return True"]}, {"cell_type": "code", "execution_count": 3, "id": "43c918f6", "metadata": {}, "outputs": [], "source": ["def search(pat, txt):\n", "    M = len(pat)\n", "    N = len(txt)\n", "\n", "    countP = [0]*MAX\n", "    countTW = [0]*MAX\n", "\n", "    for i in range(M):\n", "        (countP[ord(pat[i])]) += 1\n", "        (countTW[ord(txt[i])]) += 1\n", "\n", "    for i in range(M, N):\n", "        if compare(countP, countTW):\n", "            print(\"Found at Index\",(i-M))\n", "\n", "        (countTW[ord(txt[i])]) += 1\n", "        (countTW[ord(txt[i-M])]) -= 1\n", "\n", "    if compare(countP, countTW):\n", "        print(\"Found at Index\", N-M)"]}, {"cell_type": "code", "execution_count": 4, "id": "1f15142e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found at Index 0\n", "Found at Index 5\n", "Found at Index 6\n"]}], "source": ["txt = \"BACDGABCDA\"\n", "pat = \"ABCD\"\n", "search(pat, txt)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}