{"cells": [{"cell_type": "markdown", "id": "eb4d5209", "metadata": {}, "source": ["# Task: Binary Search using `bisect` module in Python\n", "\n", "## Problem Statement:\n", "Given a **sorted list**, perform Binary Search to **find the first occurrence** of an element using Python’s built-in `bisect` module.\n", "\n", "## Steps:\n", "\n", "1. **Import** the `bisect` module.\n", "2. Use `bisect.bisect_left(list, x)` to find the **insertion point** for `x` in the list.\n", "3. Check if the element at that position is equal to `x`.\n", "   - If yes, return the index (first occurrence).\n", "   - If not, return -1 (element not found).\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2d4f16f3", "metadata": {}, "outputs": [], "source": ["from bisect import bisect_left"]}, {"cell_type": "code", "execution_count": 2, "id": "3ea15e47", "metadata": {}, "outputs": [], "source": ["def BinarySearch(a,x):\n", "    i = bisect_left(a, x)\n", "    if i != len(a) and a[i] == x:\n", "        return i\n", "    else:\n", "        return -1"]}, {"cell_type": "code", "execution_count": 3, "id": "27f5d1b2", "metadata": {}, "outputs": [], "source": ["a = [1,2,4,4,8]\n", "x = int(4)"]}, {"cell_type": "code", "execution_count": 4, "id": "8f4323bc", "metadata": {}, "outputs": [], "source": ["res = BinarySearch(a,x)"]}, {"cell_type": "code", "execution_count": 5, "id": "dc6384be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First occurrence of 4 is present at 2\n"]}], "source": ["if res == -1:\n", "    print(x, 'is absent')\n", "else:\n", "    print(\"First occurrence of\", x, \"is present at\", res)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}