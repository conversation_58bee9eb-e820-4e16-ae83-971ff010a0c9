{"cells": [{"cell_type": "markdown", "id": "dda82f9b", "metadata": {}, "source": ["# Mo<PERSON>le 11: Mile Stone Project 2(<PERSON>)"]}, {"cell_type": "markdown", "id": "b45de012", "metadata": {}, "source": ["## Black Jack Game"]}, {"cell_type": "code", "execution_count": 1, "id": "f78a0858", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "suits = ('Hearts', 'Diamonds', 'Spades', 'Clubs')\n", "ranks = ('Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', '<PERSON>', 'Queen', 'King', '<PERSON>')\n", "values = {'Two':2, 'Three':3, 'Four':4, 'Five':5, 'Six':6, 'Seven':7, 'Eight':8, 'Nine':9, 'Ten':10, '<PERSON>':10, 'Queen':10, 'King':10, '<PERSON>':11}\n", "\n", "playing = True"]}, {"cell_type": "code", "execution_count": 2, "id": "07b98a14", "metadata": {}, "outputs": [], "source": ["class Card:\n", "    def __init__(self, suit, rank):\n", "        self.suit = suit\n", "        self.rank = rank\n", "    \n", "    def __str__(self):\n", "        return self.rank + \" of \" + self.suit "]}, {"cell_type": "code", "execution_count": 3, "id": "ed6ac8c9", "metadata": {}, "outputs": [], "source": ["class Deck:\n", "    def __init__(self):\n", "        self.deck = []\n", "        for suit in suits:\n", "            for rank in ranks:\n", "                self.deck.append(Card(suit, rank))\n", "    \n", "    def __str__(self):\n", "        deck_comp = ''\n", "        for card in self.deck:\n", "            deck_comp += '\\n' + card.__str__()\n", "        return \"The deck has: \" + deck_comp\n", "    \n", "    def shuffle(self):\n", "        random.shuffle(self.deck)\n", "    \n", "    def deal_one(self):\n", "        single_card = self.deck.pop()\n", "        return single_card"]}, {"cell_type": "code", "execution_count": 4, "id": "b3828479", "metadata": {}, "outputs": [], "source": ["class Hand:\n", "    def __init__(self):\n", "        self.cards = []\n", "        self.value = 0\n", "        self.aces = 0\n", "    \n", "    def add_card(self, card):\n", "        self.cards.append(card)\n", "        self.value += values[card.rank]\n", "        if card.rank == 'Ace':\n", "            self.aces += 1\n", "    \n", "    def adjust_for_ace(self):\n", "        while self.value > 21 and self.aces:\n", "            self.value -= 10\n", "            self.aces -= 1"]}, {"cell_type": "code", "execution_count": 5, "id": "d0b7c204", "metadata": {}, "outputs": [], "source": ["class Chips:\n", "\n", "    def __init__(self,total = 100):\n", "        self.total = total\n", "        self.bet = 0\n", "\n", "    def win_bet(self):\n", "        self.total += self.bet\n", "\n", "    def lose_bet(self):\n", "        self.total -= self.bet"]}, {"cell_type": "code", "execution_count": 6, "id": "fbe0ef59", "metadata": {}, "outputs": [], "source": ["def take_bet(chips):\n", "\n", "    while True:\n", "        try:\n", "            chips.bet = int(input(\"How many chips would you like to bet? \"))\n", "        except ValueError:\n", "            print(\"Sorry, a bet must be an integer!\")\n", "        else:\n", "            if chips.bet > chips.total:\n", "                print(f\"Sorry, your bet can't exceed {chips.total}\")\n", "            elif chips.bet <= 0:\n", "                print(\"Sorry, your bet must be greater than 0\")\n", "            else:\n", "                break"]}, {"cell_type": "code", "execution_count": 7, "id": "495182ad", "metadata": {}, "outputs": [], "source": ["def hit(deck, hand):\n", "    hand.add_card(deck.deal_one())\n", "    hand.adjust_for_ace()"]}, {"cell_type": "code", "execution_count": 8, "id": "6d55f742", "metadata": {}, "outputs": [], "source": ["def hit_or_stand(deck, hand):\n", "    global playing\n", "    \n", "    while True:\n", "        x = input(\"Would you like to Hit or Stand? Enter 'h' or 's' \")\n", "        \n", "        if x[0].lower() == 'h':\n", "            hit(deck, hand)\n", "        \n", "        elif x[0].lower() == 's':\n", "            print(\"Player stands. Dealer is playing.\")\n", "            playing = False\n", "        \n", "        else:\n", "            print(\"Sorry, please try again.\")\n", "            continue\n", "        break"]}, {"cell_type": "code", "execution_count": 9, "id": "85e7f427", "metadata": {}, "outputs": [], "source": ["def show_some(player, dealer):\n", "    print(\"\\nDealer's Hand:\")\n", "    print(\" <card hidden>\")\n", "    print('',dealer.cards[1])  \n", "    print(\"\\nPlayer's Hand:\", *player.cards, sep='\\n ')\n", "    \n", "def show_all(player, dealer):\n", "    print(\"\\nDealer's Hand:\", *dealer.cards, sep='\\n ')\n", "    print(\"Dealer's Hand =\",dealer.value)\n", "    print(\"\\nPlayer's Hand:\", *player.cards, sep='\\n ')\n", "    print(\"Player's Hand =\",player.value)\n", "\n", "def player_busts(player, dealer, chips):\n", "    print(\"Player busts!\")\n", "    chips.lose_bet()\n", "\n", "def player_wins(player, dealer, chips):\n", "    print(\"Player wins!\")\n", "    chips.win_bet()\n", "\n", "def dealer_busts(player, dealer, chips):\n", "    print(\"Dealer busts!\")\n", "    chips.win_bet()\n", "    \n", "def dealer_wins(player, dealer, chips):\n", "    print(\"Dealer wins!\")\n", "    chips.lose_bet()\n", "    \n", "def push(player, dealer):\n", "    print(\"Dealer and Player tie! It's a push.\")"]}, {"cell_type": "code", "execution_count": 10, "id": "1409baad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to BLACKJACK\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dealer's Hand:\n", " <card hidden>\n", " Two of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Two of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", " Four of Clubs\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Two of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", " Four of Clubs\n", " Two of Clubs\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Two of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", " Four of Clubs\n", " Two of Clubs\n", " Four of Hearts\n", "Player stands. Dealer is playing.\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Two of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", " Four of Clubs\n", " Two of Clubs\n", " Four of Hearts\n", "\n", "Dealer's Hand:\n", " Six of Hearts\n", " Two of Diamonds\n", " Eight of Spades\n", " King of Hearts\n", "Dealer's Hand = 26\n", "\n", "Player's Hand:\n", " Seven of Diamonds\n", " Ace of Hearts\n", " Four of Clubs\n", " Two of Clubs\n", " Four of Hearts\n", "Player's Hand = 18\n", "Dealer busts!\n", "\n", "Player's winnings stand at 2000\n", "Welcome to BLACKJACK\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Jack of Spades\n", "\n", "Player's Hand:\n", " Two of Clubs\n", " Ten of Spades\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Jack of Spades\n", "\n", "Player's Hand:\n", " Two of Clubs\n", " Ten of Spades\n", " Ace of Spades\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Jack of Spades\n", "\n", "Player's Hand:\n", " Two of Clubs\n", " Ten of Spades\n", " Ace of Spades\n", " Eight of Hearts\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Jack of Spades\n", "\n", "Player's Hand:\n", " Two of Clubs\n", " Ten of Spades\n", " Ace of Spades\n", " Eight of Hearts\n", " Six of Spades\n", "Player busts!\n", "\n", "Player's winnings stand at 500\n", "Welcome to BLACKJACK\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Seven of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Spades\n", " Four of Spades\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Seven of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Spades\n", " Four of Spades\n", " Queen of Hearts\n", "\n", "Dealer's Hand:\n", " <card hidden>\n", " Seven of Diamonds\n", "\n", "Player's Hand:\n", " Seven of Spades\n", " Four of Spades\n", " Queen of Hearts\n", " Four of Clubs\n", "Player busts!\n", "\n", "Player's winnings stand at 500\n", "Thanks for playing!\n"]}], "source": ["while True:\n", "\n", "    print(\"Welcome to BLACKJACK\")\n", "\n", "    deck = Deck()\n", "    deck.shuffle()\n", "\n", "    player_hand = Hand()\n", "    player_hand.add_card(deck.deal_one())\n", "    player_hand.add_card(deck.deal_one())\n", "\n", "    dealer_hand = Hand()\n", "    dealer_hand.add_card(deck.deal_one())\n", "    dealer_hand.add_card(deck.deal_one())\n", "\n", "    player_chips = Chips(1000)\n", "\n", "    take_bet(player_chips)\n", "\n", "    show_some(player_hand, dealer_hand)\n", "\n", "    while playing:\n", "        hit_or_stand(deck, player_hand)\n", "        show_some(player_hand, dealer_hand)\n", "\n", "        if player_hand.value > 21:\n", "            player_busts(player_hand, dealer_hand, player_chips)\n", "            break\n", "\n", "    if player_hand.value <= 21:\n", "\n", "        while dealer_hand.value < 17:\n", "            hit(deck, dealer_hand)\n", "\n", "        show_all(player_hand, dealer_hand)\n", "\n", "        if dealer_hand.value > 21:\n", "            dealer_busts(player_hand, dealer_hand, player_chips)\n", "\n", "        elif dealer_hand.value > player_hand.value:\n", "            dealer_wins(player_hand, dealer_hand, player_chips)\n", "\n", "        elif player_hand.value > dealer_hand.value:\n", "            player_wins(player_hand, dealer_hand, player_chips)\n", "\n", "        else:\n", "            push(player_hand, dealer_hand)\n", "\n", "    print(\"\\nPlayer's winnings stand at\", player_chips.total)\n", "\n", "    new_game = input(\"Would you like to play another hand? Enter 'y' or 'n' \")\n", "    if new_game[0].lower() == \"y\":\n", "        playing = True\n", "        continue\n", "    else:\n", "        print(\"Thanks for playing!\")\n", "        break"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}