{"cells": [{"cell_type": "markdown", "id": "1cfee969-6015-4233-8e44-e3b2bbfcb07a", "metadata": {}, "source": ["# Task: Build Flashcard Using Class in Python\n", "\n", "## Problem Statement:\n", "Create a flashcard system in Python using classes. Each flashcard should contain a word and its meaning, enabling easy display and review.\n", "\n", "### Steps:\n", "1. Take word and its meaning as input from the user.\n", "2. Define a class named `Flashcard` with an `__init__()` method to initialize word and meaning attributes.\n", "3. Implement the `__str__()` method to return a formatted string containing the word and its meaning.\n", "4. Store instances of the flashcard in a list.\n", "5. Use a `while` loop to display all flashcards one by one.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "babc2814-8a98-49ec-b04a-84cbef260e55", "metadata": {}, "outputs": [], "source": ["class Flashcard():\n", "    def __init__(self,word,meaning):\n", "        self.word = word\n", "        self.meaning = meaning\n", "\n", "    def __str__(self):\n", "        return self.word+' ('+self.meaning+')'"]}, {"cell_type": "code", "execution_count": 2, "id": "5bbb8d1e-d08d-4afc-9839-7e6da9c7b628", "metadata": {}, "outputs": [], "source": ["flash = []"]}, {"cell_type": "code", "execution_count": 3, "id": "53720995-43de-4666-bb16-bfd6f632700c", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["Enter the word you want to add to the flashcard:  DSA\n", "Enter the meaning of the word:  Algorithm\n", "enter 0 , if you want to add another flashcard :  0\n", "Enter the word you want to add to the flashcard:  GFG\n", "Enter the meaning of the word:  GeeksForGeeks\n", "enter 0 , if you want to add another flashcard :  1\n"]}], "source": ["while(True):\n", "    word = input(\"Enter the word you want to add to the flashcard: \")\n", "    meaning = input(\"Enter the meaning of the word: \")\n", "\n", "    flash.append(Flashcard(word,meaning))\n", "\n", "    option = int(input(\"enter 0 , if you want to add another flashcard : \"))\n", "\n", "    if(option):\n", "        break"]}, {"cell_type": "code", "execution_count": 4, "id": "db9c8d1f-0346-406b-bf60-4c470832b97a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Your flashcards\n", "> DSA (Algorithm)\n", "> GFG (GeeksForGeeks)\n"]}], "source": ["print(\"\\nYour flashcards\")\n", "for i in flash:\n", "    print(\">\", i)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}