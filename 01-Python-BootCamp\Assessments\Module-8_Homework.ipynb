{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Object Oriented Programming\n", "## Homework Assignment\n", "\n", "#### Problem 1\n", "Fill in the Line class methods to accept coordinates as a pair of tuples and return the slope and distance of the line."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["class Line:\n", "    \n", "    def __init__(self,coor1,coor2):\n", "        self.x1 , self.y1 = coor1\n", "        self.x2 , self.y2 = coor2\n", "    \n", "    def distance(self):\n", "        upper = self.y2 - self.y1\n", "        lowwer = self.x2 - self.x1\n", "        print(math.sqrt((math.pow(upper,2) + math.pow(lowwer,2))))\n", "    \n", "    def slope(self):\n", "        print(((self.y2 - self.y1) / (self.x2 - self.x1)))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# EXAMPLE OUTPUT\n", "\n", "coordinate1 = (3,2)\n", "coordinate2 = (8,10)\n", "\n", "li = Line(coordinate1,coordinate2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9.433981132056603\n"]}], "source": ["import math\n", "li.distance()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.6\n"]}], "source": ["li.slope()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["________\n", "#### Problem 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Fill in the class "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["class Cylinder:\n", "    \n", "    def __init__(self,height=1,radius=1):\n", "        self.height = height\n", "        self.radius = radius\n", "        \n", "    def volume(self):\n", "        print(math.pi*self.radius*self.radius*self.height)\n", "    \n", "    def surface_area(self):\n", "        print(2*math.pi*self.radius*(self.radius+self.height))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# EXAMPLE OUTPUT\n", "c = Cylind<PERSON>(2,3)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["56.548667764616276\n"]}], "source": ["c.volume()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["94.24777960769379\n"]}], "source": ["c.surface_area()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}