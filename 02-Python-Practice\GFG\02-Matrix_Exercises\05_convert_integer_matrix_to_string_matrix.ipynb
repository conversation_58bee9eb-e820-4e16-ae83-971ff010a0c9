{"cells": [{"cell_type": "markdown", "id": "24b8ffad-ed6d-44e2-a00d-17a021fb4451", "metadata": {}, "source": ["# Task: Convert <PERSON><PERSON><PERSON> to String Matrix\n", "\n", "## Problem Statement:\n", "Given a matrix with integer values, convert each element to a string using `str()` and list comprehension.\n", "\n", "### Steps:\n", "1. Iterate over each row in the matrix.\n", "2. For each row, use list comprehension to apply `str()` to each element.\n", "3. Return the new matrix where each integer is converted to a string.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d8c0149e-ba5c-4392-9af6-df11952aa5ce", "metadata": {}, "outputs": [], "source": ["test_list = [[4,5,7],[10,8,3],[19,4,6],[9,3,6]]"]}, {"cell_type": "code", "execution_count": 2, "id": "a12665f9-dd8e-4699-b7a5-6f15bb4d1ded", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The original list : [[4, 5, 7], [10, 8, 3], [19, 4, 6], [9, 3, 6]]\n"]}], "source": ["print(\"The original list : \" + str(test_list))"]}, {"cell_type": "code", "execution_count": 3, "id": "5bd30ee8-c885-4089-9f6d-349e73b44142", "metadata": {}, "outputs": [], "source": ["res = [[str(ele) for ele in sub] for sub in test_list]"]}, {"cell_type": "code", "execution_count": 4, "id": "08816aae-167a-45bb-bbc7-cc49cf9b9ccb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The data type converted Matrix : [['4', '5', '7'], ['10', '8', '3'], ['19', '4', '6'], ['9', '3', '6']]\n"]}], "source": ["print(\"The data type converted Matrix : \" + str(res))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}