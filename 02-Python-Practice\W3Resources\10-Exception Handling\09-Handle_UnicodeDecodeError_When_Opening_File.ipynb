{"cells": [{"cell_type": "markdown", "id": "e9623643", "metadata": {}, "source": ["# Task: Handle UnicodeDecodeError When Opening a File\n", "\n", "## Problem Statement:\n", "Write a Python program that attempts to open and read a file. If a `UnicodeDecodeError` occurs due to encoding issues, the program should catch the exception and handle it gracefully—either by notifying the user or attempting to reopen the file with a different encoding.\n", "\n", "## Steps:\n", "1. Use a `try-except` block around the file-reading operation.\n", "2. Attempt to open and read the file using the default encoding (e.g., `'r'` mode).\n", "3. Catch the `UnicodeDecodeError` and notify the user of the encoding issue.\n", "4. Optionally, attempt to reopen the file with a different encoding (e.g., `'utf-8-sig'`, `'latin-1'`, etc.) or prompt the user for input.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c88c8ee6", "metadata": {}, "outputs": [], "source": ["def open_file(filename):\n", "    encoding = input(\"Enter the encoding (ASCII, UTF-16, UTF-8) for the file: \")\n", "    print(encoding)\n", "    try:\n", "        with open(filename,'r',encoding=encoding) as file:\n", "            contents = file.read()\n", "            print(\"File Contetns:\")\n", "            print(contents)\n", "    except UnicodeDecodeError:\n", "        print(\"Error: Encoding issue occured while reading the file.\")"]}, {"cell_type": "code", "execution_count": 2, "id": "c9b7bdd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ASCII\n", "Error: Encoding issue occured while reading the file.\n"]}], "source": ["filename = input(\"Enter the file name: \")\n", "open_file(filename)"]}, {"cell_type": "code", "execution_count": 3, "id": "7a6c8332", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UTF-16\n", "Error: Encoding issue occured while reading the file.\n"]}], "source": ["filename = input(\"Enter the file name: \")\n", "open_file(filename)"]}, {"cell_type": "code", "execution_count": 4, "id": "65966def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UTF-8\n", "File Contetns:\n", "Hello,你好,привет,مرحبا, שלום !\n", "\n", "This file contains Unicode characters from different languages.\n", "\n", "日本語のテキストも含まれています。\n", "\n", "Enjoy exploring the world of Unicode!\n"]}], "source": ["filename = input(\"Enter the file name: \")\n", "open_file(filename)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}