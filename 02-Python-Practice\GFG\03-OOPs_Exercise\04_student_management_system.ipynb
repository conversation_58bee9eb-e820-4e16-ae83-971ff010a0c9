{"cells": [{"cell_type": "markdown", "id": "626f3332-66c5-4bc0-a4bd-8c9da54e8a39", "metadata": {}, "source": ["# Task: Student Management System in Python\n", "\n", "## Problem Statement:\n", "Write a Python program to build a simple Student Management System that can perform the following operations:\n", "- Accept student data\n", "- Display all student records\n", "- Search for a student by attribute\n", "- Delete a student record\n", "- Update existing student information\n", "\n", "### Steps:\n", "1. Define a data structure (like a list of dictionaries or objects) to store student records.\n", "2. Implement a function to accept and add new student data.\n", "3. Implement a display function to show all stored student records.\n", "4. Create a search function to find a student based on a given attribute (e.g., ID or name).\n", "5. Add a delete function to remove a student record.\n", "6. Implement an update function to modify details of an existing student.\n", "7. Use a menu or loop to allow users to choose operations interactively.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "460037e2-3487-43f7-9b21-35a516efae35", "metadata": {}, "outputs": [], "source": ["class Student:\n", "\n", "    #init\n", "    def __init__(self,name,rollno,m1,m2):\n", "        self.name = name\n", "        self.rollno = rollno\n", "        self.m1 = m1\n", "        self.m2 = m2\n", "\n", "    def accept(self,name,rollno,marks1,marks2):\n", "        new_student = Student(name,rollno,marks1,marks2)\n", "        students_list.append(new_student)\n", "\n", "    def display(self,student_obj):\n", "        print(f'Name: {student_obj.name}')\n", "        print(f'Roll No.: {student_obj.rollno}')\n", "        print(f'Marks 1: {student_obj.m1}')\n", "        print(f'Marks 2: {student_obj.m2}')\n", "        print('\\n')\n", "\n", "    def search(self,rollno):\n", "        for index in range(len(students_list)):\n", "            if students_list[index].rollno == rollno:\n", "                return index\n", "\n", "    def delete(self, rollno):\n", "        index = self.search(rollno)\n", "        if index is not None:\n", "            del students_list[index]\n", "            print(f\"Student with roll number {rollno} deleted.\")\n", "        else:\n", "            print(\"Student not found.\")\n", "\n", "    def update(self, old_rollno, new_rollno):\n", "        index = self.search(old_rollno)\n", "        if index is not None:\n", "            students_list[index].rollno = new_rollno\n", "            print(f\"Student with roll number {old_rollno} updated to {new_rollno}.\")\n", "        else:\n", "            print(\"Student not found.\")"]}, {"cell_type": "code", "execution_count": 2, "id": "60757513-0aa2-40e2-8f98-9ce4c14d8fc7", "metadata": {}, "outputs": [], "source": ["def display_all_students():\n", "    if students_list:\n", "        print('\\nList of Students\\n')\n", "        for student in students_list:\n", "            obj.display(student)"]}, {"cell_type": "code", "execution_count": 3, "id": "4d6754fd-3a96-4dda-8b2e-ca8585c61397", "metadata": {}, "outputs": [], "source": ["def add_student():\n", "    name = input(\"Enter student's name: \")\n", "    rollno = int(input(\"Enter student's roll number: \"))\n", "    marks1 = int(input(\"Enter marks for subject 1: \"))\n", "    marks2 = int(input(\"Enter marks for subject 2: \"))\n", "    obj.accept(name,rollno,marks1,marks2)\n", "    print(f'Student {name} added successfully.')"]}, {"cell_type": "code", "execution_count": 4, "id": "d08a8b59-83f9-4af2-971d-9d77edab23a5", "metadata": {}, "outputs": [], "source": ["def search_student():\n", "    rollno = int(input(\"Enter roll number of the student to search: \"))\n", "    student_index = obj.search(rollno)\n", "\n", "    if student_index is not None:\n", "        obj.display(students_list[student_index])\n", "    else:\n", "        print(\"Student not found.\")"]}, {"cell_type": "code", "execution_count": 5, "id": "62252ec1-3cc7-434b-82ab-832e5a37a1a9", "metadata": {}, "outputs": [], "source": ["def delete_student():\n", "    rollno = int(input(\"Enter roll number of the student to delete: \"))\n", "    obj.delete(rollno)"]}, {"cell_type": "code", "execution_count": 6, "id": "e659e754-4245-4f7a-84ec-fc1fcdfaf35e", "metadata": {}, "outputs": [], "source": ["def update_student():\n", "    old_rollno = int(input(\"Enter roll number of the student to update: \"))\n", "    new_rollno = int(input(\"Enter new 00roll number: \"))\n", "\n", "    obj.update(old_rollno,new_rollno)"]}, {"cell_type": "code", "execution_count": 7, "id": "d5d2d646-912e-442a-ada5-a51036406db3", "metadata": {}, "outputs": [], "source": ["students_list = []"]}, {"cell_type": "code", "execution_count": 8, "id": "1f7d63ac-b972-475c-b280-90b1db38a9fe", "metadata": {}, "outputs": [], "source": ["obj = Student('', 0, 0, 0)"]}, {"cell_type": "code", "execution_count": 9, "id": "c82cbce3-bdf2-47cd-b60f-fa84c5b97bb7", "metadata": {}, "outputs": [], "source": ["# Adding some students manually to begin with\n", "obj.accept('A',1,100,100)\n", "obj.accept('B',2,90,90)\n", "obj.accept('C',3,80,80)"]}, {"cell_type": "code", "execution_count": 10, "id": "74f6a4bb-550c-4aee-bde2-266e3479b7ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  1\n", "Enter student's name:  D\n", "Enter student's roll number:  4\n", "Enter marks for subject 1:  85\n", "Enter marks for subject 2:  90\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Student D added successfully.\n", "\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "List of Students\n", "\n", "Name: A\n", "Roll No.: 1\n", "Marks 1: 100\n", "Marks 2: 100\n", "\n", "\n", "Name: B\n", "Roll No.: 2\n", "Marks 1: 90\n", "Marks 2: 90\n", "\n", "\n", "Name: C\n", "Roll No.: 3\n", "Marks 1: 80\n", "Marks 2: 80\n", "\n", "\n", "Name: D\n", "Roll No.: 4\n", "Marks 1: 85\n", "Marks 2: 90\n", "\n", "\n", "\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  3\n", "Enter roll number of the student to search:  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Name: B\n", "Roll No.: 2\n", "Marks 1: 90\n", "Marks 2: 90\n", "\n", "\n", "\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  4\n", "Enter roll number of the student to delete:  4\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Student with roll number 4 deleted.\n", "\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "List of Students\n", "\n", "Name: A\n", "Roll No.: 1\n", "Marks 1: 100\n", "Marks 2: 100\n", "\n", "\n", "Name: B\n", "Roll No.: 2\n", "Marks 1: 90\n", "Marks 2: 90\n", "\n", "\n", "Name: C\n", "Roll No.: 3\n", "Marks 1: 80\n", "Marks 2: 80\n", "\n", "\n", "\n", "Operations Available:\n", "1. Add Student Details\n", "2. <PERSON><PERSON><PERSON> All Student Details\n", "3. Search for a Student\n", "4. Delete Student\n", "5. Update Student Details\n", "6. Exit\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  6\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Thank you! Exiting...\n"]}], "source": ["while True:\n", "    print('\\nOperations Available:')\n", "    print('1. Add Student Details')\n", "    print('2. <PERSON><PERSON><PERSON> All Student Details')\n", "    print('3. Search for a Student')\n", "    print('4. Delete Student')\n", "    print('5. Update Student Details')\n", "    print('6. Exit')\n", "\n", "    choice = int(input(\"Enter your choice: \"))\n", "\n", "    if choice == 1:\n", "        add_student()\n", "    elif choice == 2:\n", "        display_all_students()\n", "    elif choice == 3:\n", "        search_student()\n", "    elif choice == 4:\n", "        delete_student()\n", "    elif choice == 5:\n", "        update_student()\n", "    elif choice == 6:\n", "        print(\"Thank you! Exiting...\")\n", "        break\n", "    else:\n", "        print(\"Invlaid Choice. Please try again.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}