{"cells": [{"cell_type": "markdown", "id": "6d3aff71", "metadata": {}, "source": ["# Task: Binary Search (Recursive and Iterative) - Python\n", "\n", "## Problem Statement:\n", "Given a **sorted** array `arr` and an element `x`, implement two versions of Binary Search in Python:\n", "- **Recursive Binary Search**\n", "- **Iterative Binary Search**\n", "\n", "Both should return the **index** of `x` in the array if found, or **-1** if not found.\n", "\n", "## Steps:\n", "\n", "### Recursive Binary Search\n", "1. **Define** a function `binary_search_recursive(arr, low, high, x)`.\n", "2. If `high >= low`:\n", "   - Compute the middle index `mid = (low + high) // 2`.\n", "   - If `arr[mid] == x`, return `mid`.\n", "   - If `arr[mid] > x`, recursively search the left half.\n", "   - <PERSON><PERSON>, recursively search the right half.\n", "3. If `low > high`, return `-1` (element not found).\n", "\n", "### Iterative Binary Search\n", "1. **Define** a function `binary_search_iterative(arr, x)`.\n", "2. Initialize `low = 0`, `high = len(arr) - 1`.\n", "3. While `low <= high`:\n", "   - Compute `mid = (low + high) // 2`.\n", "   - If `arr[mid] == x`, return `mid`.\n", "   - If `arr[mid] < x`, set `low = mid + 1`.\n", "   - <PERSON><PERSON>, set `high = mid - 1`.\n", "4. If loop ends, return `-1` (element not found).\n"]}, {"cell_type": "markdown", "id": "223c6f87", "metadata": {}, "source": ["## Recursive"]}, {"cell_type": "code", "execution_count": 6, "id": "043cbc6c", "metadata": {}, "outputs": [], "source": ["def binary_search_recursive(arr, low, high, x):\n", "    if high >= low:\n", "\n", "        mid = (high + low) // 2\n", "\n", "        if arr[mid] == x:\n", "            return mid\n", "\n", "        elif arr[mid] > x:\n", "            return binary_search_recursive(arr, low, mid - 1, x)\n", "\n", "        else:\n", "            return binary_search_recursive(arr, mid + 1, high, x)\n", "\n", "    else:\n", "        return -1"]}, {"cell_type": "code", "execution_count": 7, "id": "0033285c", "metadata": {}, "outputs": [], "source": ["arr = [ 2, 3, 4, 10, 40 ]\n", "x = 10"]}, {"cell_type": "code", "execution_count": 8, "id": "e08b5609", "metadata": {}, "outputs": [], "source": ["result = binary_search_recursive(arr, 0, len(arr)-1, x)"]}, {"cell_type": "code", "execution_count": 9, "id": "b95e7b97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Element is present at index 3\n"]}], "source": ["if result != -1:\n", "    print(\"Element is present at index\", str(result))\n", "else:\n", "    print(\"Element is not present in array\")"]}, {"cell_type": "markdown", "id": "9730a463", "metadata": {}, "source": ["## Iterative"]}, {"cell_type": "code", "execution_count": 10, "id": "37473da9", "metadata": {}, "outputs": [], "source": ["def binary_search_iterative(arr, x):\n", "    low = 0\n", "    high = len(arr) - 1\n", "    mid = 0\n", "\n", "    while low <= high:\n", "\n", "        mid = (high + low) // 2\n", "\n", "        if arr[mid] < x:\n", "            low = mid + 1\n", "\n", "        elif arr[mid] > x:\n", "            high = mid - 1\n", "\n", "        else:\n", "            return mid\n", "\n", "    return -1"]}, {"cell_type": "code", "execution_count": 11, "id": "acc92202", "metadata": {}, "outputs": [], "source": ["result = binary_search_iterative(arr, x)"]}, {"cell_type": "code", "execution_count": 12, "id": "83a8b26a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Element is present at index 3\n"]}], "source": ["if result != -1:\n", "    print(\"Element is present at index\", str(result))\n", "else:\n", "    print(\"Element is not present in array\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}